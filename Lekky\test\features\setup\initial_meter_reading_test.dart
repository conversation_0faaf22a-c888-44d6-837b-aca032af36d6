import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:lekky/features/setup/domain/services/initial_meter_reading_service.dart';
import 'package:lekky/features/setup/domain/services/setup_completion_service.dart';
import 'package:lekky/features/setup/domain/models/setup_preferences.dart';
import 'package:lekky/features/setup/data/repositories/preferences_repository.dart';
import 'package:lekky/features/meter_readings/domain/repositories/meter_reading_repository.dart';
import 'package:lekky/core/shared/models/date_format.dart';
import 'package:lekky/core/shared/models/theme_mode.dart';

@GenerateMocks([MeterReadingRepository, PreferencesRepository])
import 'initial_meter_reading_test.mocks.dart';

void main() {
  group('InitialMeterReadingService', () {
    late InitialMeterReadingService service;
    late MockMeterReadingRepository mockRepository;

    setUp(() {
      mockRepository = MockMeterReadingRepository();
      service = InitialMeterReadingService(mockRepository);
    });

    test('should create initial meter reading successfully', () async {
      // Arrange
      when(mockRepository.addMeterReading(any)).thenAnswer((_) async => 1);

      // Act
      final result = await service.createInitialMeterReading(50.0, DateTime.now());

      // Assert
      expect(result, true);
      verify(mockRepository.addMeterReading(any)).called(1);
    });

    test('should reject negative values', () async {
      // Act
      final result = await service.createInitialMeterReading(-10.0, DateTime.now());

      // Assert
      expect(result, false);
      verifyNever(mockRepository.addMeterReading(any));
    });

    test('should reject values over 999.99', () async {
      // Act
      final result = await service.createInitialMeterReading(1000.0, DateTime.now());

      // Assert
      expect(result, false);
      verifyNever(mockRepository.addMeterReading(any));
    });

    test('should accept valid values at boundaries', () async {
      // Arrange
      when(mockRepository.addMeterReading(any)).thenAnswer((_) async => 1);

      // Act & Assert
      expect(await service.createInitialMeterReading(0.0, DateTime.now()), true);
      expect(await service.createInitialMeterReading(999.99, DateTime.now()), true);
    });
  });

  group('SetupCompletionService', () {
    late SetupCompletionService service;
    late MockPreferencesRepository mockPrefsRepo;
    late MockMeterReadingRepository mockMeterRepo;
    late InitialMeterReadingService mockInitialService;

    setUp(() {
      mockPrefsRepo = MockPreferencesRepository();
      mockMeterRepo = MockMeterReadingRepository();
      mockInitialService = InitialMeterReadingService(mockMeterRepo);
      service = SetupCompletionService(mockPrefsRepo, mockInitialService);
    });

    test('should complete setup successfully with initial meter reading', () async {
      // Arrange
      final preferences = SetupPreferences(
        language: 'English',
        currency: 'GBP',
        currencySymbol: '£',
        initialMeterReading: 50.0,
        alertThreshold: 10.0,
        daysInAdvance: 3,
        dateFormat: DateFormat.ddMmYyyy,
        showTimeWithDate: false,
        themeMode: AppThemeMode.system,
      );

      when(mockPrefsRepo.savePreferences(any)).thenAnswer((_) async {});
      when(mockPrefsRepo.markSetupCompleted()).thenAnswer((_) async {});
      when(mockMeterRepo.addMeterReading(any)).thenAnswer((_) async => 1);

      // Act
      final result = await service.completeSetup(preferences);

      // Assert
      expect(result.success, true);
      expect(result.meterReadingCreated, true);
      expect(result.hasError, false);
    });

    test('should complete setup successfully without initial meter reading', () async {
      // Arrange
      final preferences = SetupPreferences(
        language: 'English',
        currency: 'GBP',
        currencySymbol: '£',
        initialMeterReading: null,
        alertThreshold: 10.0,
        daysInAdvance: 3,
        dateFormat: DateFormat.ddMmYyyy,
        showTimeWithDate: false,
        themeMode: AppThemeMode.system,
      );

      when(mockPrefsRepo.savePreferences(any)).thenAnswer((_) async {});
      when(mockPrefsRepo.markSetupCompleted()).thenAnswer((_) async {});

      // Act
      final result = await service.completeSetup(preferences);

      // Assert
      expect(result.success, true);
      expect(result.meterReadingCreated, true); // Still true as no reading was attempted
      expect(result.hasError, false);
      verifyNever(mockMeterRepo.addMeterReading(any));
    });
  });
}
