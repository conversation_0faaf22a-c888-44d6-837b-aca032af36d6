# Navigation Architecture for NewLekky App

This document outlines the navigation architecture for the NewLekky app, including routes, transitions, and navigation patterns.

## Navigation Strategy

The NewLekky app will use go_router for navigation, providing a clean, declarative approach to routing with support for deep linking and state preservation.

## Route Structure

### Main Routes

| Route | Path | Description |
|-------|------|-------------|
| **Splash** | `/splash` | Initial loading screen |
| **Welcome** | `/welcome` | First-time user experience |
| **Setup** | `/setup` | Initial configuration |
| **Home** | `/home` | Main dashboard |
| **History** | `/history` | Entry history and management |
| **Cost** | `/cost` | Cost calculations and projections |
| **Settings** | `/settings` | App settings and preferences |

### Sub-Routes

| Parent | Sub-Route | Path | Description |
|--------|-----------|------|-------------|
| **Setup** | Language | `/setup/language` | Language selection |
| **Setup** | Currency | `/setup/currency` | Currency configuration |
| **Setup** | Initial Reading | `/setup/initial-reading` | First meter reading |
| **Settings** | General | `/settings/general` | General settings |
| **Settings** | Notifications | `/settings/notifications` | Notification settings |
| **Settings** | Data | `/settings/data` | Data management |
| **Settings** | About | `/settings/about` | App information |

### Dialog Routes

| Route | Path | Description |
|-------|------|-------------|
| **Add Entry** | `/add-entry` | Add new meter reading or top-up |
| **Edit Entry** | `/edit-entry/:id` | Edit existing entry |
| **Delete Confirmation** | `/delete-confirmation/:id` | Confirm entry deletion |
| **Filter Options** | `/filter-options` | Configure history filters |

## Navigation Implementation

### Router Configuration

```dart
final router = GoRouter(
  initialLocation: '/splash',
  routes: [
    GoRoute(
      path: '/splash',
      builder: (context, state) => SplashScreen(),
    ),
    GoRoute(
      path: '/welcome',
      builder: (context, state) => WelcomeScreen(),
    ),
    // Additional routes...
  ],
);
```

### Navigation Patterns

#### Bottom Navigation

The main app screens (Home, Cost, History) will be accessible via the bottom navigation bar. This will be implemented using a ShellRoute in go_router:

```dart
ShellRoute(
  builder: (context, state, child) => ScaffoldWithBottomNav(child: child),
  routes: [
    GoRoute(
      path: '/home',
      builder: (context, state) => HomeScreen(),
    ),
    GoRoute(
      path: '/cost',
      builder: (context, state) => CostScreen(),
    ),
    GoRoute(
      path: '/history',
      builder: (context, state) => HistoryScreen(),
    ),
  ],
),
```

#### Dialog Navigation

Dialogs will be implemented as routes to support deep linking and back button handling:

```dart
GoRoute(
  path: '/add-entry',
  pageBuilder: (context, state) => CustomTransitionPage(
    child: AddEntryDialog(),
    transitionsBuilder: (context, animation, secondaryAnimation, child) {
      return FadeTransition(opacity: animation, child: child);
    },
  ),
),
```

## Transitions

### Screen Transitions

| Transition | Description | Usage |
|------------|-------------|-------|
| **Slide** | Slide from right to left | Default screen transition |
| **Fade** | Fade in/out | Splash to Welcome transition |
| **None** | No animation | Bottom navigation tab switching |

### Dialog Transitions

| Transition | Description | Usage |
|------------|-------------|-------|
| **Scale** | Scale up from center | Add/Edit Entry dialogs |
| **Fade** | Fade in/out | Confirmation dialogs |
| **Slide Up** | Slide up from bottom | Filter Options dialog |

## Deep Linking

The app will support deep linking to specific screens and states:

### External Deep Links

| Link | Action |
|------|--------|
| `lekky://home` | Open home screen |
| `lekky://history` | Open history screen |
| `lekky://add-entry` | Open add entry dialog |
| `lekky://settings` | Open settings screen |

### Internal Deep Links

| Link | Action |
|------|--------|
| `lekky://edit-entry/:id` | Open edit dialog for specific entry |
| `lekky://history?filter=invalid` | Open history with invalid entries filter |
| `lekky://cost?period=monthly` | Open cost screen with monthly view |

## State Preservation

The navigation system will preserve state during navigation:

1. **Screen State**: Preserve scroll position, selected tabs, etc.
2. **Form State**: Preserve input values when navigating away and back
3. **Filter State**: Remember applied filters in the history screen

Implementation:

```dart
GoRoute(
  path: '/history',
  builder: (context, state) {
    // Extract and apply saved state
    final filter = state.queryParams['filter'];
    return HistoryScreen(initialFilter: filter);
  },
),
```

## Navigation Service

A navigation service will be implemented to provide a clean API for navigation:

```dart
class NavigationService {
  // Navigate to named route
  Future<T?> navigateTo<T>(String routeName, {Map<String, String>? params});
  
  // Navigate back
  void goBack();
  
  // Replace current route
  Future<T?> replaceCurrent<T>(String routeName, {Map<String, String>? params});
  
  // Show dialog
  Future<T?> showDialogRoute<T>(String dialogRoute, {Map<String, String>? params});
}
```

## Error Handling

The navigation system will handle errors gracefully:

1. **Route Not Found**: Redirect to home screen
2. **Invalid Parameters**: Use safe defaults
3. **Navigation Failures**: Log error and recover

## Accessibility Considerations

1. **Screen Announcements**: Announce screen changes to screen readers
2. **Focus Management**: Properly manage focus during navigation
3. **Keyboard Navigation**: Support keyboard navigation between routes

## Implementation Guidelines

1. Define all routes in a central location (`lib/core/navigation/routes.dart`)
2. Use constants for route names to avoid string literals
3. Implement a navigation service for clean navigation API
4. Add analytics tracking for screen views
5. Test navigation flows with integration tests
6. Support system back button on Android
7. Handle deep links in AppDelegate (iOS) and MainActivity (Android)
