import 'package:flutter/material.dart';

/// Custom color extension for Lekky-specific colors
@immutable
class LekkyColors extends ThemeExtension<LekkyColors> {
  final Color homeAppBar;
  final Color historyAppBar;
  final Color costAppBar;
  final Color settingsAppBar;
  final Color notificationAppBar;
  final Color textValue;
  final Color textCurrency;
  final Color warningIndicator;
  final Color errorIndicator;
  final Color addButton;

  const LekkyColors({
    required this.homeAppBar,
    required this.historyAppBar,
    required this.costAppBar,
    required this.settingsAppBar,
    required this.notificationAppBar,
    required this.textValue,
    required this.textCurrency,
    required this.warningIndicator,
    required this.errorIndicator,
    required this.addButton,
  });

  @override
  LekkyColors copyWith({
    Color? homeAppBar,
    Color? historyAppBar,
    Color? costAppBar,
    Color? settingsAppBar,
    Color? notificationAppBar,
    Color? textValue,
    Color? textCurrency,
    Color? warningIndicator,
    Color? errorIndicator,
    Color? addButton,
  }) {
    return LekkyColors(
      homeAppBar: homeAppBar ?? this.homeAppBar,
      historyAppBar: historyAppBar ?? this.historyAppBar,
      costAppBar: costAppBar ?? this.costAppBar,
      settingsAppBar: settingsAppBar ?? this.settingsAppBar,
      notificationAppBar: notificationAppBar ?? this.notificationAppBar,
      textValue: textValue ?? this.textValue,
      textCurrency: textCurrency ?? this.textCurrency,
      warningIndicator: warningIndicator ?? this.warningIndicator,
      errorIndicator: errorIndicator ?? this.errorIndicator,
      addButton: addButton ?? this.addButton,
    );
  }

  @override
  LekkyColors lerp(ThemeExtension<LekkyColors>? other, double t) {
    if (other is! LekkyColors) {
      return this;
    }
    return LekkyColors(
      homeAppBar: Color.lerp(homeAppBar, other.homeAppBar, t)!,
      historyAppBar: Color.lerp(historyAppBar, other.historyAppBar, t)!,
      costAppBar: Color.lerp(costAppBar, other.costAppBar, t)!,
      settingsAppBar: Color.lerp(settingsAppBar, other.settingsAppBar, t)!,
      notificationAppBar:
          Color.lerp(notificationAppBar, other.notificationAppBar, t)!,
      textValue: Color.lerp(textValue, other.textValue, t)!,
      textCurrency: Color.lerp(textCurrency, other.textCurrency, t)!,
      warningIndicator:
          Color.lerp(warningIndicator, other.warningIndicator, t)!,
      errorIndicator: Color.lerp(errorIndicator, other.errorIndicator, t)!,
      addButton: Color.lerp(addButton, other.addButton, t)!,
    );
  }
}

/// Extension methods to easily access theme colors
extension ThemeExtensions on ThemeData {
  LekkyColors get lekkyColors => extension<LekkyColors>()!;

  /// Get the appropriate app bar color based on the current screen
  Color getAppBarColor(String screen) {
    final colors = extension<LekkyColors>()!;
    switch (screen.toLowerCase()) {
      case 'home':
        return colors.homeAppBar;
      case 'history':
        return colors.historyAppBar;
      case 'cost':
        return colors.costAppBar;
      case 'settings':
        return colors.settingsAppBar;
      default:
        return primaryColor;
    }
  }
}
