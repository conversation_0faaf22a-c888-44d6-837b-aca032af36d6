import 'package:flutter/material.dart';

/// A card widget for settings sections
class SettingsCard extends StatefulWidget {
  /// Title of the card
  final String title;

  /// Icon to display
  final IconData icon;

  /// Child widgets to display when expanded
  final List<Widget> children;

  /// Whether the card is initially expanded
  final bool initiallyExpanded;

  /// Whether the card is enabled
  final bool enabled;

  /// Callback when enabled state changes
  final Function(bool)? onEnabledChanged;

  /// Constructor
  const SettingsCard({
    super.key,
    required this.title,
    required this.icon,
    required this.children,
    this.initiallyExpanded = false,
    this.enabled = true,
    this.onEnabledChanged,
  });

  @override
  State<SettingsCard> createState() => _SettingsCardState();
}

class _SettingsCardState extends State<SettingsCard> {
  late bool _isExpanded;
  late bool _isEnabled;

  @override
  void initState() {
    super.initState();
    _isExpanded = widget.initiallyExpanded;
    _isEnabled = widget.enabled;
  }

  @override
  void didUpdateWidget(SettingsCard oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.enabled != widget.enabled) {
      _isEnabled = widget.enabled;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(8.0),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.0),
      ),
      child: Column(
        children: [
          ListTile(
            leading: Icon(
              widget.icon,
              color: _isEnabled
                  ? Theme.of(context).colorScheme.primary
                  : Theme.of(context).disabledColor,
            ),
            title: Text(
              widget.title,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: _isEnabled
                    ? Theme.of(context).textTheme.bodyLarge?.color
                    : Theme.of(context).disabledColor,
              ),
            ),
            trailing: widget.onEnabledChanged != null
                ? Switch(
                    value: _isEnabled,
                    onChanged: (value) {
                      setState(() {
                        _isEnabled = value;
                        if (!value) {
                          _isExpanded = false;
                        }
                      });
                      widget.onEnabledChanged?.call(value);
                    },
                    activeColor: Theme.of(context).colorScheme.primary,
                  )
                : IconButton(
                    icon: Icon(
                      _isExpanded ? Icons.expand_less : Icons.expand_more,
                      color: _isEnabled
                          ? Theme.of(context).colorScheme.primary
                          : Theme.of(context).disabledColor,
                    ),
                    onPressed: _isEnabled
                        ? () {
                            setState(() {
                              _isExpanded = !_isExpanded;
                            });
                          }
                        : null,
                  ),
            onTap: _isEnabled
                ? () {
                    setState(() {
                      _isExpanded = !_isExpanded;
                    });
                  }
                : null,
          ),
          if (_isExpanded && _isEnabled)
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: widget.children,
              ),
            ),
        ],
      ),
    );
  }
}
