import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'currency_input_field.dart';
import 'settings_section_header.dart';

/// A widget for configuring alert settings
class AlertSettingsCard extends StatelessWidget {
  /// Alert threshold value
  final double alertThreshold;

  /// Days in advance value
  final int daysInAdvance;

  /// Currency symbol
  final String currencySymbol;

  /// Callback when alert threshold changes
  final Function(double) onAlertThresholdChanged;

  /// Callback when days in advance changes
  final Function(int) onDaysInAdvanceChanged;

  /// Constructor
  const AlertSettingsCard({
    super.key,
    required this.alertThreshold,
    required this.daysInAdvance,
    required this.currencySymbol,
    required this.onAlertThresholdChanged,
    required this.onDaysInAdvanceChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(8.0),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.0),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SettingsSectionHeader(
              title: 'Alert Settings',
              description: 'Configure when you want to receive alerts.',
              icon: Icons.notifications,
            ),

            // Alert Threshold Subsection
            const Text(
              'Alert Threshold',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            const Text(
              'Get notified when your balance falls below this amount.',
              style: TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 8),

            _buildAlertThresholdInput(context),

            const SizedBox(height: 24),

            // Days in Advance Subsection
            const Text(
              'Days in Advance',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            const Text(
              'Get notified this many days before you run out.',
              style: TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 8),

            _buildDaysInAdvanceInput(context),
          ],
        ),
      ),
    );
  }

  Widget _buildAlertThresholdInput(BuildContext context) {
    return CurrencyInputField(
      value: alertThreshold,
      onChanged: (value) =>
          value != null ? onAlertThresholdChanged(value) : null,
      currencySymbol: currencySymbol,
      labelText: 'Alert Threshold',
      hintText: 'Enter amount',
      minValue: 1.00,
      maxValue: 999.99,
    );
  }

  Widget _buildDaysInAdvanceInput(BuildContext context) {
    final controller = TextEditingController(
      text: daysInAdvance.toString(),
    );

    return TextField(
      controller: controller,
      keyboardType: TextInputType.number,
      inputFormatters: [
        FilteringTextInputFormatter.digitsOnly,
      ],
      decoration: const InputDecoration(
        labelText: 'Days in Advance',
        hintText: 'Enter days',
        suffixText: 'days',
        border: OutlineInputBorder(),
        contentPadding: EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 12,
        ),
        helperText: 'Must be between 1 and 99 days',
      ),
      onChanged: (value) {
        if (value.isNotEmpty) {
          final newValue = int.tryParse(value);
          if (newValue != null && newValue >= 1 && newValue <= 99) {
            onDaysInAdvanceChanged(newValue);
          }
        }
      },
      showCursor: true,
      autofocus: false,
    );
  }
}
