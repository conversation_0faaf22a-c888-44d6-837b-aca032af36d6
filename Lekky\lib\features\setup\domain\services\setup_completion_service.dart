import '../../../../core/utils/logger.dart';
import '../models/setup_preferences.dart';
import '../../data/repositories/preferences_repository.dart';
import 'initial_meter_reading_service.dart';

/// Service that handles all setup completion logic
class SetupCompletionService {
  final PreferencesRepository _preferencesRepository;
  final InitialMeterReadingService _initialMeterReadingService;

  SetupCompletionService(
    this._preferencesRepository,
    this._initialMeterReadingService,
  );

  /// Complete the setup process
  /// Returns SetupCompletionResult with success status and any errors
  Future<SetupCompletionResult> completeSetup(
      SetupPreferences preferences) async {
    try {
      Logger.info('SetupCompletionService: Starting setup completion');

      // Step 1: Save preferences
      await _preferencesRepository.savePreferences(preferences);
      Logger.info('SetupCompletionService: Preferences saved successfully');

      // Step 2: Create initial meter reading if provided
      bool meterReadingSuccess = true;
      String? meterReadingError;

      if (preferences.initialMeterReading != null) {
        Logger.info(
            'SetupCompletionService: Creating initial meter reading with value ${preferences.initialMeterReading}');

        meterReadingSuccess =
            await _initialMeterReadingService.createInitialMeterReading(
          preferences.initialMeterReading!,
          DateTime.now(),
        );

        if (!meterReadingSuccess) {
          meterReadingError = 'Failed to create initial meter reading';
          Logger.warning(
              'SetupCompletionService: Initial meter reading creation failed, but continuing setup completion');
        }
      } else {
        Logger.info(
            'SetupCompletionService: No initial meter reading provided, skipping');
      }

      // Step 3: Mark setup as completed
      await _preferencesRepository.markSetupCompleted();
      Logger.info('SetupCompletionService: Setup marked as completed');

      return SetupCompletionResult(
        success: true,
        meterReadingCreated: meterReadingSuccess,
        error: meterReadingError,
      );
    } catch (e) {
      Logger.error('SetupCompletionService: Setup completion failed: $e');
      return SetupCompletionResult(
        success: false,
        meterReadingCreated: false,
        error: 'Setup completion failed: ${e.toString()}',
      );
    }
  }
}

/// Result of setup completion operation
class SetupCompletionResult {
  final bool success;
  final bool meterReadingCreated;
  final String? error;

  SetupCompletionResult({
    required this.success,
    required this.meterReadingCreated,
    this.error,
  });

  bool get hasError => error != null;
}
