import 'package:flutter/material.dart';

/// Utility class for responsive text sizing and layout calculations
class ResponsiveTextUtils {
  // Private constructor to prevent instantiation
  ResponsiveTextUtils._();

  /// Screen size breakpoints
  static const double smallScreenThreshold = 360.0;
  static const double mediumScreenThreshold = 600.0;

  /// Font size configurations for different screen sizes
  static const double largeFontSize = 36.0;
  static const double mediumFontSize = 32.0;
  static const double smallFontSize = 28.0;
  static const double minimumFontSize = 20.0;

  /// Calculate responsive font size based on screen width and content constraints
  static double calculateResponsiveFontSize({
    required BuildContext context,
    required String text,
    required double availableWidth,
    TextStyle? baseStyle,
  }) {
    final screenWidth = MediaQuery.of(context).size.width;
    
    // Determine starting font size based on screen size
    double startingFontSize;
    if (screenWidth < smallScreenThreshold) {
      startingFontSize = smallFontSize;
    } else if (screenWidth < mediumScreenThreshold) {
      startingFontSize = mediumFontSize;
    } else {
      startingFontSize = largeFontSize;
    }

    // Test if text fits at starting font size
    final testStyle = (baseStyle ?? const TextStyle()).copyWith(
      fontSize: startingFontSize,
    );

    if (_doesTextFit(text, testStyle, availableWidth)) {
      return startingFontSize;
    }

    // Progressively reduce font size until text fits or minimum is reached
    for (double fontSize = startingFontSize - 2; 
         fontSize >= minimumFontSize; 
         fontSize -= 2) {
      final scaledStyle = testStyle.copyWith(fontSize: fontSize);
      if (_doesTextFit(text, scaledStyle, availableWidth)) {
        return fontSize;
      }
    }

    return minimumFontSize;
  }

  /// Calculate font size for two texts that need to fit in the same row
  static double calculateDualTextFontSize({
    required BuildContext context,
    required String text1,
    required String text2,
    required double availableWidth,
    required double spacingBetween,
    TextStyle? baseStyle,
  }) {
    final screenWidth = MediaQuery.of(context).size.width;
    
    // Determine starting font size based on screen size
    double startingFontSize;
    if (screenWidth < smallScreenThreshold) {
      startingFontSize = smallFontSize;
    } else if (screenWidth < mediumScreenThreshold) {
      startingFontSize = mediumFontSize;
    } else {
      startingFontSize = largeFontSize;
    }

    // Test if both texts fit at starting font size
    final testStyle = (baseStyle ?? const TextStyle()).copyWith(
      fontSize: startingFontSize,
    );

    final totalWidth = _getTextWidth(text1, testStyle) + 
                      _getTextWidth(text2, testStyle) + 
                      spacingBetween;

    if (totalWidth <= availableWidth) {
      return startingFontSize;
    }

    // Progressively reduce font size until both texts fit or minimum is reached
    for (double fontSize = startingFontSize - 2; 
         fontSize >= minimumFontSize; 
         fontSize -= 2) {
      final scaledStyle = testStyle.copyWith(fontSize: fontSize);
      final scaledTotalWidth = _getTextWidth(text1, scaledStyle) + 
                              _getTextWidth(text2, scaledStyle) + 
                              spacingBetween;
      
      if (scaledTotalWidth <= availableWidth) {
        return fontSize;
      }
    }

    return minimumFontSize;
  }

  /// Check if text fits within the given width
  static bool _doesTextFit(String text, TextStyle style, double maxWidth) {
    final textWidth = _getTextWidth(text, style);
    return textWidth <= maxWidth;
  }

  /// Calculate the width required for a text with given style
  static double _getTextWidth(String text, TextStyle style) {
    final textPainter = TextPainter(
      text: TextSpan(text: text, style: style),
      textDirection: TextDirection.ltr,
    );
    textPainter.layout();
    return textPainter.width;
  }
}
