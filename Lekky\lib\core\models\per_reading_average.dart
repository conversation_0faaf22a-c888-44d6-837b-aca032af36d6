import 'package:equatable/equatable.dart';

/// Model class for per-reading recent averages
class PerReadingAverage extends Equatable {
  /// Unique identifier
  final int? id;
  
  /// ID of the meter reading this average is for
  final int meterReadingId;
  
  /// Date of the meter reading
  final DateTime readingDate;
  
  /// Recent average usage per day for the period ending with this reading
  final double recentAveragePerDay;
  
  /// Date and time when this average was calculated
  final DateTime calculatedAt;

  /// Constructor
  const PerReadingAverage({
    this.id,
    required this.meterReadingId,
    required this.readingDate,
    required this.recentAveragePerDay,
    required this.calculatedAt,
  });

  /// Create a copy of this average with the given fields replaced
  PerReadingAverage copyWith({
    int? id,
    int? meterReadingId,
    DateTime? readingDate,
    double? recentAveragePerDay,
    DateTime? calculatedAt,
  }) {
    return PerReadingAverage(
      id: id ?? this.id,
      meterReadingId: meterReadingId ?? this.meterReadingId,
      readingDate: readingDate ?? this.readingDate,
      recentAveragePerDay: recentAveragePerDay ?? this.recentAveragePerDay,
      calculatedAt: calculatedAt ?? this.calculatedAt,
    );
  }

  /// Convert to a map for database operations
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'meter_reading_id': meterReadingId,
      'reading_date': readingDate.toIso8601String(),
      'recent_average_per_day': recentAveragePerDay,
      'calculated_at': calculatedAt.toIso8601String(),
    };
  }

  /// Create a per-reading average from a map
  factory PerReadingAverage.fromMap(Map<String, dynamic> map) {
    return PerReadingAverage(
      id: map['id'] as int?,
      meterReadingId: map['meter_reading_id'] as int,
      readingDate: DateTime.parse(map['reading_date'] as String),
      recentAveragePerDay: map['recent_average_per_day'] as double,
      calculatedAt: DateTime.parse(map['calculated_at'] as String),
    );
  }

  @override
  List<Object?> get props => [id, meterReadingId, readingDate, recentAveragePerDay, calculatedAt];

  @override
  String toString() {
    return 'PerReadingAverage(id: $id, meterReadingId: $meterReadingId, readingDate: $readingDate, recentAveragePerDay: $recentAveragePerDay, calculatedAt: $calculatedAt)';
  }
}
