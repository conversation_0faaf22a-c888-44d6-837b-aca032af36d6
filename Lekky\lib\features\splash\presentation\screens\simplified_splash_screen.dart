// File: lib/features/splash/presentation/screens/simplified_splash_screen.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../../core/constants/app_constants.dart';
import '../widgets/simplified_splash_animation.dart';

// Provider for managing the first run state
final isFirstRunProvider = StateProvider<bool>((ref) {
  // Initialize with null and load from SharedPreferences asynchronously
  return true; // Default to true until loaded
});

/// The splash screen of the app
class SimplifiedSplashScreen extends ConsumerStatefulWidget {
  const SimplifiedSplashScreen({super.key});

  @override
  ConsumerState<SimplifiedSplashScreen> createState() => _SimplifiedSplashScreenState();
}

class _SimplifiedSplashScreenState extends ConsumerState<SimplifiedSplashScreen> {
  @override
  void initState() {
    super.initState();
    _loadFirstRunStatus();
  }

  Future<void> _loadFirstRunStatus() async {
    final prefs = await SharedPreferences.getInstance();
    final isFirstRun = prefs.getBool('isFirstRun') ?? true;
    ref.read(isFirstRunProvider.notifier).state = isFirstRun;

    // Simulate a delay for the splash screen
    await Future.delayed(const Duration(seconds: 2));

    if (!mounted) return;

    // Navigate based on first run status
    if (ref.read(isFirstRunProvider)) {
      Navigator.of(context).pushReplacementNamed(AppConstants.routeWelcome);
    } else {
      Navigator.of(context).pushReplacementNamed(AppConstants.routeHome);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          color: Color(0xFF003087), // Deep blue to match welcome screen
        ),
        child: const Center(
          child: SimplifiedSplashAnimation(),
        ),
      ),
    );
  }
}