# Dialog and Card Specifications for NewLekky App

This document provides comprehensive specifications for dialog boxes and cards in the NewLekky app, ensuring consistent design and behavior across the application.

## Dialog Boxes

### General Dialog Specifications

#### Size and Layout

- **Width**: 85% of screen width, maximum 500dp
- **Height**: Auto (content-based), maximum 80% of screen height
- **Margins**: 24dp from screen edges (when not full-width)
- **Padding**: 24dp for content area
- **Corner Radius**: 16dp for all corners

#### Animation

- **Entry Animation**: 
  - Fade in (0.0 to 1.0 opacity)
  - Scale up (0.8 to 1.0 scale)
  - Duration: 250ms
  - Curve: fastOutSlowIn
- **Exit Animation**:
  - Fade out (1.0 to 0.0 opacity)
  - Scale down (1.0 to 0.9 scale)
  - Duration: 200ms
  - Curve: fastOutSlowIn

#### Structure

- **Title Bar**:
  - Height: 56dp
  - Title text: 20sp, medium weight
  - Optional icon: 24dp × 24dp, left of title
  - Close button: 48dp × 48dp touch target, right aligned
- **Content Area**:
  - Flexible layout based on content
  - Scrollable when content exceeds maximum height
- **Button Bar**:
  - Height: 52dp
  - Padding: 8dp top, 8dp bottom, 24dp horizontal
  - Button alignment: right-aligned for confirmation dialogs, stretched for input dialogs

#### Elevation and Shadow

- **Elevation**: 24dp
- **Shadow Color**: 
  - Light mode: Black with 30% opacity
  - Dark mode: Black with 60% opacity

### Dialog Types

#### Add Entry Dialog

- **Title**: "Add Entry" with plus icon
- **Content**:
  - Entry type selector (segmented control)
  - Date/time picker
  - Value/amount input field
  - Optional notes field
- **Buttons**:
  - Cancel (secondary)
  - Save (primary)
- **Validation**:
  - Real-time validation with error messages
  - Save button disabled until valid input

#### Edit Entry Dialog

- **Title**: "Edit Entry" with pencil icon
- **Content**:
  - Pre-filled fields with current entry data
  - Same fields as Add Entry Dialog
- **Buttons**:
  - Delete (danger, left-aligned)
  - Cancel (secondary)
  - Save (primary)
- **Validation**:
  - Same as Add Entry Dialog
  - Confirmation required for delete action

#### Delete Confirmation Dialog

- **Title**: "Delete Entry" with trash icon
- **Content**:
  - Warning message about permanent deletion
  - Entry details summary
- **Buttons**:
  - Cancel (secondary)
  - Delete (danger)
- **Behavior**:
  - No auto-dismiss
  - Haptic feedback before deletion

#### Settings Dialog

- **Title**: Based on setting category
- **Content**:
  - Setting-specific controls
  - Description text when needed
- **Buttons**:
  - Cancel (secondary)
  - Apply/Save (primary)
- **Behavior**:
  - Preview changes when possible
  - Confirm unsaved changes on cancel

### Dialog Behavior

#### Dismissal

- **Tap Outside**: Dismisses dialog (equivalent to Cancel)
- **Back Button**: Dismisses dialog (equivalent to Cancel)
- **Swipe**: No swipe dismissal support for standard dialogs

#### Keyboard Handling

- **Enter Key**: Triggers primary action if enabled
- **Escape Key**: Dismisses dialog (equivalent to Cancel)
- **Tab Order**: Logical tab order through focusable elements

#### Accessibility

- **Focus**: Initial focus on first input field or primary action
- **Screen Reader**: Announces dialog title and purpose
- **Dismissal**: Clear indication of how to dismiss for screen readers

## Cards

### General Card Specifications

#### Size and Layout

- **Width**: Match parent or specified width
- **Margins**: 16dp horizontal, 8dp vertical
- **Padding**: 16dp for content area
- **Corner Radius**: 12dp for all corners

#### Elevation and Shadow

- **Default Elevation**: 4dp
- **Pressed Elevation**: 8dp
- **Focused Elevation**: 6dp
- **Shadow Color**:
  - Light mode: Black with 20% opacity
  - Dark mode: Black with 40% opacity

#### Animation

- **Touch Feedback**:
  - Ripple effect matching content color
  - Elevation change on press (4dp to 8dp)
  - Duration: 100ms
- **Entry Animation** (when appearing in lists):
  - Fade in (0.0 to 1.0 opacity)
  - Slight slide up (10dp)
  - Duration: 200ms
  - Staggered for list items (50ms delay between items)

### Card Types

#### Entry Card

- **Usage**: Display meter readings or top-ups in lists
- **Content Structure**:
  - Date/time (top-left)
  - Value/amount (top-right)
  - Type indicator (icon, left side)
  - Validity indicator (if applicable)
- **States**:
  - Normal: Standard elevation and background
  - Selected: Highlighted background, increased elevation
  - Warning: Yellow border or indicator
  - Error: Red border or indicator
- **Interactions**:
  - Tap: Select/open details
  - Long press: Show quick actions menu

#### Meter Status Card

- **Usage**: Display current meter status on home screen
- **Size**: Full width, height based on content
- **Content Structure**:
  - Current reading (large text)
  - Last updated date
  - Days remaining indicator
  - Quick action buttons
- **Elevation**: 8dp (higher than standard cards)
- **Special Features**:
  - Gradient background option
  - Prominent placement on home screen
  - Higher information density

#### Summary Card

- **Usage**: Display summary information and statistics
- **Size**: Can be used in grid layout (half-width) or full-width
- **Content Structure**:
  - Title (top)
  - Value (center, large text)
  - Subtitle or description (bottom)
  - Optional icon or indicator
- **Variants**:
  - Basic: Single value with title
  - Split: Two related values side by side
  - Trend: Value with trend indicator

#### Settings Card

- **Usage**: Group related settings
- **Content Structure**:
  - Section title
  - Settings items with controls
  - Dividers between items
- **Interactions**:
  - Individual tap targets for each setting
  - No card-level interaction

### Card Behavior

#### Interaction States

- **Rest**: Default appearance
- **Hover/Focus**: Subtle highlight, increased elevation
- **Pressed**: Ripple effect, increased elevation
- **Disabled**: Reduced opacity (70%), no interaction

#### Selection Behavior

- **Single Selection**: Highlight selected card, maintain selection until changed
- **Multi-Selection**: Checkbox appears, multiple cards can be selected
- **Deselection**: Tap selected card or tap elsewhere

#### Swipe Actions

- **Supported Cards**: Entry cards in list views
- **Swipe Right**: Quick action (e.g., edit)
- **Swipe Left**: Delete with confirmation
- **Swipe Threshold**: 60% of card width to trigger action

## Implementation Guidelines

### Dialog Implementation

```dart
showDialog(
  context: context,
  barrierDismissible: true,
  builder: (BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.0),
      ),
      elevation: 24.0,
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Dialog title
            Row(
              children: [
                Icon(Icons.add, color: Theme.of(context).primaryColor),
                SizedBox(width: 8.0),
                Text(
                  'Add Entry',
                  style: TextStyle(
                    fontSize: 20.0,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Spacer(),
                IconButton(
                  icon: Icon(Icons.close),
                  onPressed: () => Navigator.of(context).pop(),
                ),
              ],
            ),
            // Dialog content
            // ...
            // Button bar
            Padding(
              padding: const EdgeInsets.only(top: 16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    child: Text('CANCEL'),
                    onPressed: () => Navigator.of(context).pop(),
                  ),
                  SizedBox(width: 8.0),
                  ElevatedButton(
                    child: Text('SAVE'),
                    onPressed: () {
                      // Save logic
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  },
);
```

### Card Implementation

```dart
Card(
  elevation: 4.0,
  shape: RoundedRectangleBorder(
    borderRadius: BorderRadius.circular(12.0),
  ),
  child: InkWell(
    onTap: () {
      // Handle tap
    },
    borderRadius: BorderRadius.circular(12.0),
    child: Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '03-05-2025',
                style: TextStyle(
                  color: Theme.of(context).textTheme.bodyText1?.color,
                ),
              ),
              Text(
                '30.25',
                style: TextStyle(
                  color: Theme.of(context).colorScheme.secondary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          // Additional content
        ],
      ),
    ),
  ),
);
```

## Accessibility Considerations

- Ensure all interactive elements have minimum touch target size of 48×48dp
- Provide sufficient color contrast (4.5:1 for normal text, 3:1 for large text)
- Support dynamic text sizing
- Include appropriate semantic labels for screen readers
- Ensure keyboard navigability for all interactive elements

## Responsive Behavior

- On small screens (<360dp width):
  - Reduce padding to 16dp
  - Consider full-width dialogs
- On large screens (>600dp width):
  - Center dialogs with maximum width
  - Consider multi-column card layouts
- On extra large screens (>960dp width):
  - Maintain maximum dialog width
  - Use grid layouts for cards where appropriate
