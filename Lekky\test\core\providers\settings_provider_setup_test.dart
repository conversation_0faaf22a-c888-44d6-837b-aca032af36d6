import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:lekky/core/providers/settings_provider.dart';
import 'package:lekky/features/setup/domain/models/setup_preferences.dart';
import 'package:lekky/core/shared/models/date_format.dart';
import 'package:lekky/core/shared/models/theme_mode.dart';

void main() {
  group('SettingsProvider Setup Completion', () {
    late ProviderContainer container;

    setUp(() async {
      // Clear SharedPreferences before each test
      SharedPreferences.setMockInitialValues({});
      container = ProviderContainer();
    });

    tearDown(() {
      container.dispose();
    });

    test('should complete setup successfully with valid preferences', () async {
      // Arrange
      final preferences = SetupPreferences(
        language: 'English',
        currency: 'GBP',
        currencySymbol: '£',
        initialMeterReading: 50.0,
        alertThreshold: 10.0,
        daysInAdvance: 5,
        dateFormat: DateFormat.ddMmYyyy,
        showTimeWithDate: true,
        themeMode: AppThemeMode.system,
      );

      // Act
      final result = await container
          .read(settingsProvider.notifier)
          .completeSetup(preferences);

      // Assert
      expect(result.success, true);
      // Note: hasError may be true due to meter reading creation failing in test environment
      // This is expected behavior when database is not available
    });

    test('should complete setup successfully without initial meter reading',
        () async {
      // Arrange
      final preferences = SetupPreferences(
        language: 'English',
        currency: 'GBP',
        currencySymbol: '£',
        initialMeterReading: null,
        alertThreshold: 10.0,
        daysInAdvance: 5,
        dateFormat: DateFormat.ddMmYyyy,
        showTimeWithDate: true,
        themeMode: AppThemeMode.system,
      );

      // Act
      final result = await container
          .read(settingsProvider.notifier)
          .completeSetup(preferences);

      // Assert
      expect(result.success, true);
      expect(result.hasError, false); // No meter reading to create, so no error
    });

    test('should fail setup with invalid initial meter reading', () async {
      // Arrange
      final preferences = SetupPreferences(
        language: 'English',
        currency: 'GBP',
        currencySymbol: '£',
        initialMeterReading: 1000.0, // Invalid: > 999.99
        alertThreshold: 10.0,
        daysInAdvance: 5,
        dateFormat: DateFormat.ddMmYyyy,
        showTimeWithDate: true,
        themeMode: AppThemeMode.system,
      );

      // Act
      final result = await container
          .read(settingsProvider.notifier)
          .completeSetup(preferences);

      // Assert
      expect(result.success, true); // Setup still succeeds
      expect(result.hasError, true); // But with meter reading error
      expect(result.error,
          contains('Initial meter reading must be between 0.00 and 999.99'));
    });

    test('should update settings state after successful setup', () async {
      // Arrange
      final preferences = SetupPreferences(
        language: 'Spanish',
        currency: 'EUR',
        currencySymbol: '€',
        initialMeterReading: null,
        alertThreshold: 15.0,
        daysInAdvance: 7,
        dateFormat: DateFormat.ddMmYyyy,
        showTimeWithDate: false,
        themeMode: AppThemeMode.dark,
      );

      // Act
      await container
          .read(settingsProvider.notifier)
          .completeSetup(preferences);

      // Assert
      final settingsState = await container.read(settingsProvider.future);
      expect(settingsState.language, 'Spanish');
      expect(settingsState.currency, 'EUR');
      expect(settingsState.currencySymbol, '€');
      expect(settingsState.alertThreshold, 15.0);
      expect(settingsState.daysInAdvance, 7);
      expect(settingsState.showTimeWithDate, false);
      expect(settingsState.themeMode, AppThemeMode.dark);
    });
  });
}
