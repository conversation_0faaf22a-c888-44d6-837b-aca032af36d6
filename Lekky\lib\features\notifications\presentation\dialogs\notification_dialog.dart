import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/notification_provider.dart';
import '../widgets/notification_item.dart';
import '../../../../core/widgets/app_banner.dart';
import '../../../../core/widgets/lekky_button.dart';
import '../../../../core/theme/app_colors.dart';
import '../../domain/models/notification_state.dart';
import '../../domain/models/notification.dart';

/// Dialog for viewing and managing notifications
class NotificationDialog extends ConsumerWidget {
  const NotificationDialog({super.key});

  /// Calculate responsive dialog width
  double _getDialogWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final calculatedWidth = screenWidth < 600
        ? screenWidth * 0.95 // Small and medium screens
        : 500.0; // Fixed width for larger screens

    debugPrint(
        'NotificationDialog: screenWidth=$screenWidth, calculatedWidth=$calculatedWidth');
    return calculatedWidth;
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final notificationAsync = ref.watch(notificationProvider);
    final screenWidth = MediaQuery.of(context).size.width;
    final dialogWidth = _getDialogWidth(context);
    final horizontalPadding = (screenWidth - dialogWidth) / 2;

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      elevation: 24,
      insetPadding: EdgeInsets.symmetric(
        horizontal: horizontalPadding,
        vertical: 28, // Same as edit entry dialog for 7% increased height
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // App Banner with gradient colors, notification icon, and close button
          AppBanner(
            message: 'Notifications',
            icon: Icons.notifications,
            gradientColors: AppColors.getNotificationMainCardGradient(
                Theme.of(context).brightness == Brightness.dark),
            textColor: AppColors.getAppBarTextColor('notifications',
                Theme.of(context).brightness == Brightness.dark),
            onDismiss: () => Navigator.of(context).pop(),
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(16),
              topRight: Radius.circular(16),
            ),
          ),
          // Main content based on async state
          Expanded(
            child: notificationAsync.when(
              data: (notificationState) => _buildNotificationContent(
                context,
                ref,
                notificationState,
              ),
              loading: () => const Center(
                child: CircularProgressIndicator(),
              ),
              error: (error, stackTrace) => _buildErrorContent(context, ref),
            ),
          ),
        ],
      ),
    );
  }

  /// Build notification content
  Widget _buildNotificationContent(
    BuildContext context,
    WidgetRef ref,
    NotificationState notificationState,
  ) {
    if (notificationState.notifications.isEmpty) {
      return _buildEmptyState(context);
    }

    return Column(
      children: [
        // Notifications list
        Expanded(
          child: _buildNotificationsList(context, ref, notificationState),
        ),
        // Action buttons
        _buildActionButtonsRow(context, ref, notificationState),
      ],
    );
  }

  /// Build empty state when no notifications
  Widget _buildEmptyState(BuildContext context) {
    final theme = Theme.of(context);

    return Padding(
      padding: const EdgeInsets.all(32),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.notifications_none,
            size: 64,
            color: theme.colorScheme.onSurface.withOpacity(0.3),
          ),
          const SizedBox(height: 16),
          Text(
            'No Notifications',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w500,
              color: theme.colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'You\'re all caught up! New notifications will appear here.',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 14,
              color: theme.colorScheme.onSurface.withOpacity(0.5),
            ),
          ),
        ],
      ),
    );
  }

  /// Build error content
  Widget _buildErrorContent(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);

    return Padding(
      padding: const EdgeInsets.all(32),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: theme.colorScheme.error,
          ),
          const SizedBox(height: 16),
          Text(
            'Error Loading Notifications',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w500,
              color: theme.colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Unable to load notifications. Please try again.',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 14,
              color: theme.colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
          const SizedBox(height: 16),
          LekkyButton(
            text: 'Retry',
            onPressed: () => ref.read(notificationProvider.notifier).refresh(),
            type: LekkyButtonType.primary,
            size: LekkyButtonSize.compact,
          ),
        ],
      ),
    );
  }

  /// Build notifications list with custom scrollbar and grouping by type
  Widget _buildNotificationsList(
    BuildContext context,
    WidgetRef ref,
    NotificationState notificationState,
  ) {
    // Automatic refresh on build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(notificationProvider.notifier).refresh();
    });

    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final scrollController = ScrollController();

    // Group notifications by type
    final groupedNotifications =
        _groupNotificationsByType(notificationState.sortedNotifications);

    return RawScrollbar(
      controller: scrollController,
      thumbVisibility: true,
      thickness: 6,
      thumbColor: isDarkMode
          ? Colors.grey.withOpacity(0.9)
          : Colors.grey.withOpacity(0.8),
      radius: const Radius.circular(3.0),
      padding: const EdgeInsets.only(right: 2),
      child: ListView.builder(
        controller: scrollController,
        physics: const AlwaysScrollableScrollPhysics(),
        itemCount: _getTotalItemCount(groupedNotifications),
        itemBuilder: (context, index) {
          return _buildGroupedItem(context, ref, groupedNotifications, index);
        },
      ),
    );
  }

  /// Group notifications by type
  Map<NotificationType, List<AppNotification>> _groupNotificationsByType(
      List<AppNotification> notifications) {
    final Map<NotificationType, List<AppNotification>> grouped = {};

    for (final notification in notifications) {
      if (!grouped.containsKey(notification.type)) {
        grouped[notification.type] = [];
      }
      grouped[notification.type]!.add(notification);
    }

    return grouped;
  }

  /// Get total item count including group headers
  int _getTotalItemCount(
      Map<NotificationType, List<AppNotification>> groupedNotifications) {
    int count = 0;
    for (final entry in groupedNotifications.entries) {
      count += 1; // Header
      count += entry.value.length; // Notifications
    }
    return count;
  }

  /// Build grouped item (header or notification)
  Widget _buildGroupedItem(
    BuildContext context,
    WidgetRef ref,
    Map<NotificationType, List<AppNotification>> groupedNotifications,
    int index,
  ) {
    int currentIndex = 0;

    for (final entry in groupedNotifications.entries) {
      final type = entry.key;
      final notifications = entry.value;

      // Check if this is the header
      if (currentIndex == index) {
        return _buildGroupHeader(context, type, notifications.length);
      }
      currentIndex++;

      // Check if this is one of the notifications in this group
      for (int i = 0; i < notifications.length; i++) {
        if (currentIndex == index) {
          return NotificationItem(
            notification: notifications[i],
            onMarkAsRead: (id) =>
                ref.read(notificationProvider.notifier).markAsRead(id),
            onDelete: (id) =>
                ref.read(notificationProvider.notifier).deleteNotification(id),
          );
        }
        currentIndex++;
      }
    }

    return const SizedBox.shrink();
  }

  /// Build group header
  Widget _buildGroupHeader(
      BuildContext context, NotificationType type, int count) {
    final theme = Theme.of(context);

    String title;
    IconData icon;

    switch (type) {
      case NotificationType.lowBalance:
        title = 'Low Balance Alerts';
        icon = Icons.warning;
        break;
      case NotificationType.timeToTopUp:
        title = 'Top-up Reminders';
        icon = Icons.add_card;
        break;
      case NotificationType.invalidRecord:
        title = 'Invalid Records';
        icon = Icons.error;
        break;
      case NotificationType.readingReminder:
        title = 'Reading Reminders';
        icon = Icons.schedule;
        break;
      case NotificationType.welcome:
        title = 'Welcome Messages';
        icon = Icons.info;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      margin: const EdgeInsets.only(top: 8),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceVariant.withOpacity(0.3),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(
            icon,
            size: 16,
            color: theme.colorScheme.onSurfaceVariant,
          ),
          const SizedBox(width: 8),
          Text(
            title,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
          const Spacer(),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withOpacity(0.2),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Text(
              count.toString(),
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.primary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build action buttons row
  Widget _buildActionButtonsRow(
    BuildContext context,
    WidgetRef ref,
    NotificationState notificationState,
  ) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        children: [
          // Mark All Read button
          Expanded(
            child: LekkyButton(
              text: 'Mark All Read',
              onPressed: notificationState.hasUnreadNotifications
                  ? () =>
                      ref.read(notificationProvider.notifier).markAllAsRead()
                  : null,
              type: LekkyButtonType.secondary,
            ),
          ),
          const SizedBox(width: 16),
          // Clear All button
          Expanded(
            child: LekkyButton(
              text: 'Clear All',
              onPressed: notificationState.hasNotifications
                  ? () => _showClearAllDialog(context, ref)
                  : null,
              type: LekkyButtonType.destructive,
            ),
          ),
        ],
      ),
    );
  }

  /// Show confirmation dialog for clearing all notifications
  void _showClearAllDialog(BuildContext context, WidgetRef ref) {
    final screenWidth = MediaQuery.of(context).size.width;
    final dialogWidth = screenWidth < 600 ? screenWidth * 0.95 : 500.0;
    final horizontalPadding = (screenWidth - dialogWidth) / 2;

    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        elevation: 24,
        insetPadding: EdgeInsets.symmetric(
          horizontal: horizontalPadding,
          vertical: 28,
        ),
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.warning,
                    color: Theme.of(context).colorScheme.error,
                    size: 24,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Clear All Notifications',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w500,
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => Navigator.of(context).pop(),
                    tooltip: 'Close',
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Text(
                'Are you sure you want to delete all notifications? This action cannot be undone.',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              const SizedBox(height: 24),
              Row(
                children: [
                  Expanded(
                    child: LekkyButton(
                      text: 'Cancel',
                      type: LekkyButtonType.secondary,
                      size: LekkyButtonSize.compact,
                      onPressed: () => Navigator.of(context).pop(),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: LekkyButton(
                      text: 'Clear All',
                      type: LekkyButtonType.destructive,
                      size: LekkyButtonSize.compact,
                      onPressed: () {
                        Navigator.of(context).pop();
                        ref
                            .read(notificationProvider.notifier)
                            .deleteAllNotifications();
                      },
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// Helper function to show notification dialog
void showNotificationDialog(BuildContext context) {
  showDialog(
    context: context,
    builder: (context) => const NotificationDialog(),
  );
}
