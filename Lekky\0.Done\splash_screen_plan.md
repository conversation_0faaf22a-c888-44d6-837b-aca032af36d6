# Splash Screen Plan for NewLekky

## Overview
The splash screen is the first screen users see when launching the app. It displays the app logo, name, and a loading indicator while the app initializes and checks permissions.

## Visual Design

### Background
- **Color**: Deep blue (`#003087`)
- **Style**: Solid color, no gradients or patterns

### Logo
- **Position**: Center of screen, approximately 30% from top
- **Design**: White circular background with a stylized electricity meter icon
  - The icon includes a lightning bolt symbol in blue
  - The circular background has small "feet" at the bottom to represent a meter
- **Size**: Approximately 25% of screen width

### App Name
- **Text**: "Lekky"
- **Position**: Centered, below the logo
- **Font**: Sans-serif, bold, white
- **Size**: Large (approximately 36sp)

### Tagline
- **Text**: "Your Prepaid Meter Assistant"
- **Position**: Centered, below the app name
- **Font**: Sans-serif, regular, white
- **Size**: Medium (approximately 18sp)

### Quote Box
- **Background**: Slightly lighter blue, rounded rectangle
- **Position**: Centered, below the tagline
- **Text**: "I'm not cheap—I'm kilowatt-conscious."
- **Font**: Sans-serif, italic, white
- **Size**: Medium (approximately 16sp)
- **Padding**: 16dp on all sides

### Loading Indicator
- **Type**: Circular progress indicator
- **Position**: Centered, below the quote box
- **Color**: White
- **Size**: Approximately 10% of screen width

### Status Text
- **Text**: "Checking permissions..."
- **Position**: Centered, below the loading indicator
- **Font**: Sans-serif, regular, white
- **Size**: Small (approximately 14sp)

## Animation and Timing

### Entry Animation
- Fade in logo, app name, and tagline sequentially (200ms each)
- Fade in quote box after the above elements (300ms)

### Loading Animation
- Circular progress indicator rotates continuously

### Exit Animation
- Fade out all elements simultaneously (300ms)
- Transition to Welcome screen or Home screen

### Timing
- Display splash screen for a minimum of 2 seconds
- If initialization takes longer, display until initialization is complete
- Maximum display time: 5 seconds (proceed to next screen even if initialization is still in progress)

## Functionality

### Initialization Tasks
1. Load app configuration
2. Check and request necessary permissions
3. Initialize database
4. Check for previous user data
5. Prepare navigation to appropriate next screen

### Permission Handling
- Check if required permissions are granted
- If permissions are missing, prepare to request them on the next screen
- Update status text to reflect current initialization step

### Navigation Logic
- If first-time user: Navigate to Welcome screen
- If returning user: Navigate to Home screen
- If permissions are missing: Navigate to Permission Request screen

## Implementation Details

### Architecture
```
features/splash/
├── presentation/
│   ├── screens/
│   │   └── splash_screen.dart
│   └── controllers/
│       └── splash_controller.dart
└── domain/
    └── usecases/
        ├── check_first_time_user.dart
        ├── check_permissions.dart
        └── initialize_app.dart
```

### Key Components

#### SplashScreen Widget
```dart
class SplashScreen extends StatefulWidget {
  @override
  _SplashScreenState createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  String _statusText = "Initializing...";
  
  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _initializeApp();
  }
  
  void _setupAnimations() {
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );
    
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Interval(0.0, 0.5, curve: Curves.easeIn),
      ),
    );
    
    _animationController.forward();
  }
  
  Future<void> _initializeApp() async {
    // Simulate initialization tasks
    await Future.delayed(const Duration(seconds: 1));
    setState(() => _statusText = "Checking permissions...");
    
    // Check permissions
    await Future.delayed(const Duration(seconds: 1));
    
    // Determine next screen
    final isFirstTimeUser = await checkIfFirstTimeUser();
    
    // Ensure minimum display time
    await Future.delayed(const Duration(seconds: 1));
    
    // Navigate to appropriate screen
    if (isFirstTimeUser) {
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(builder: (_) => WelcomeScreen()),
      );
    } else {
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(builder: (_) => HomeScreen()),
      );
    }
  }
  
  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.primaryBlue,
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const SizedBox(height: 80),
              Image.asset(
                'assets/images/lekky_logo.png',
                width: 100,
                height: 100,
              ),
              const SizedBox(height: 24),
              Text(
                'Lekky',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 36,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Your Prepaid Meter Assistant',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                ),
              ),
              const SizedBox(height: 32),
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 40),
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  'I\'m not cheap—I\'m kilowatt-conscious.',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ),
              const SizedBox(height: 40),
              CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
              const SizedBox(height: 24),
              Text(
                _statusText,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
```

## Testing Plan

### Unit Tests
- Test initialization logic
- Test permission checking
- Test navigation logic based on user status

### Widget Tests
- Test splash screen rendering
- Test animations
- Test status text updates

### Integration Tests
- Test full splash screen flow
- Test navigation to Welcome screen for new users
- Test navigation to Home screen for returning users

## Accessibility Considerations
- Ensure sufficient contrast between text and background
- Add semantic labels for screen readers
- Support dynamic text sizing
