// File: lib/features/settings/domain/models/settings_category.dart
import 'package:flutter/material.dart';
import 'settings_sub_branch.dart';

/// Model representing a main category in the settings screen
class SettingsCategory {
  /// The title of the category
  final String title;
  
  /// The icon to display for the category
  final IconData icon;
  
  /// The color of the icon
  final Color iconColor;
  
  /// Whether the category is enabled
  final bool isEnabled;
  
  /// Callback when the toggle is pressed
  final Function(bool) onToggle;
  
  /// Optional subtitle to display
  final String? subtitle;
  
  /// Optional current value to display
  final String? currentValue;
  
  /// List of sub-branches for this category
  final List<SettingsSubBranch> subBranches;
  
  /// Constructor
  const SettingsCategory({
    required this.title,
    required this.icon,
    required this.iconColor,
    required this.isEnabled,
    required this.onToggle,
    required this.subBranches,
    this.subtitle,
    this.currentValue,
  });
}
