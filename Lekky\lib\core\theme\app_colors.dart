// File: lib/core/theme/app_colors.dart
import 'package:flutter/material.dart';
import '../../features/notifications/domain/models/notification.dart';

/// App-wide color definitions for consistent styling across light and dark modes
class AppColors {
  // Private constructor to prevent instantiation
  AppColors._();

  // Light Theme Colors - Updated color scheme
  static const Color primary =
      Color(0xFF4A90E2); // Soft Blue - dashboard primary
  static const Color secondary =
      Color(0xFFFFA000); // Amber 700 - for energy-themed CTAs
  static const Color tertiary =
      Color(0xFFFFCA28); // Amber 300 - for badges, info chips
  static const Color error =
      Color(0xFFD32F2F); // Red 700 - meets contrast on white
  static const Color background =
      Color(0xFFF5F5F5); // Light gray - consistent background
  static const Color surface =
      Color(0xFFFFFFFF); // Pure white - for cards/sheets
  static const Color surfaceVariant =
      Color(0xFFE7E0EC); // Light variant for surfaces
  static const Color onPrimary =
      Color(0xFFFFFFFF); // White - for text on primary
  static const Color onSecondary =
      Color(0xFF000000); // Black - for text on secondary
  static const Color onTertiary =
      Color(0xFF000000); // Black - for text on tertiary
  static const Color onError = Color(0xFFFFFFFF); // White - for text on error
  static const Color onBackground =
      Color(0xFF333333); // Dark gray - for primary text
  static const Color onSurface =
      Color(0xFF333333); // Dark gray - for primary text
  static const Color onSurfaceVariant = Color(0xFF49454F);
  static const Color outline = Color(0xFF79747E);
  static const Color textSecondary =
      Color(0xFF757575); // Medium gray - for secondary text
  static const Color success =
      Color(0xFF26A69A); // Muted Teal - new accent color
  static const Color warning = Color(0xFFFFC107); // Amber - for warnings
  static const Color info =
      Color(0xFF2196F3); // Blue - for informational messages

  // Dark Theme Colors - Updated color scheme
  static const Color primaryDark =
      Color(0xFF1565C0); // Darker Blue - dashboard primary dark
  static const Color secondaryDark = Color(
      0xFFFFA000); // Amber 700 - same orange as light mode for consistency
  static const Color tertiaryDark =
      Color(0xFFFFB74D); // Amber 300 - for focal actions
  static const Color errorDark =
      Color(0xFFE57373); // Red 300 - for errors in dark mode
  static const Color backgroundDark =
      Color(0xFF121212); // Dark gray - reduces eye strain
  static const Color surfaceDark =
      Color(0xFF2C2C2E); // Card background - consistent with specs
  static const Color surfaceVariantDark =
      Color(0xFF242424); // Variant for surfaces
  static const Color onPrimaryDark =
      Color(0xFFFFFFFF); // White - for text on primary in dark mode
  static const Color onSecondaryDark =
      Color(0xFF000000); // Black - for text on secondary in dark mode
  static const Color onTertiaryDark =
      Color(0xFF000000); // Black - for text on tertiary in dark mode
  static const Color onErrorDark =
      Color(0xFF000000); // Black - for text on error in dark mode
  static const Color onBackgroundDark =
      Color(0xFFE0E0E0); // Light gray - for primary text in dark mode
  static const Color onSurfaceDark =
      Color(0xFFE0E0E0); // Light gray - for primary text in dark mode
  static const Color onSurfaceVariantDark = Color(0xFFCAC4D0);
  static const Color outlineDark = Color(0xFF938F99);
  static const Color textSecondaryDark =
      Color(0xFFB0BEC5); // Muted gray - for secondary text
  static const Color successDark =
      Color(0xFF4DB6AC); // Darker Teal - new accent color dark
  static const Color warningDark =
      Color(0xFFFFD54F); // Amber 300 - for warnings
  static const Color infoDark =
      Color(0xFF64B5F6); // Blue 300 - for informational messages

  // Transparent Colors
  static Color semiTransparentWhite = Colors.white.withOpacity(0.7);
  static Color semiTransparentBlack = Colors.black.withOpacity(0.7);
  static const Color transparent = Colors.transparent;

  // Card Background Colors
  static Color cardBackground = Colors.white.withOpacity(0.85);
  static Color cardBackgroundDark = const Color(0xFF2C2C2E).withOpacity(0.85);

  // Container Colors
  static const Color surfaceContainer = Color(0xFFEAE0EC);
  static const Color surfaceContainerDark = Color(0xFF2C2C2C);

  // Shadow Colors
  static Color shadowColor = Colors.black.withOpacity(0.1);
  static Color shadowColorDark = Colors.black.withOpacity(0.2);

  // Table Row Colors - Light Mode
  static const Color tableRowInvalid = Color(0xFFFFF9C4); // Light yellow
  static const Color tableRowTopUp = Color(0xFFFFF8E1); // Light beige
  static const Color tableRowEven = Color(0xFFFFFFFF); // White
  static const Color tableRowOdd = Color(0xFFF5F5F5); // Light gray
  static const Color tableHeaderLight = Color(0xFFE1E1E1); // Light gray header

  // Table Row Colors - Dark Mode
  static const Color tableRowInvalidDark = Color(0xFF5D4037); // Dark brown
  static const Color tableRowTopUpDark = Color(0xFF455A64); // Dark blue-gray
  static const Color tableRowEvenDark = Color(0xFF2C2C2C); // Dark gray
  static const Color tableRowOddDark = Color(0xFF212121); // Darker gray
  static const Color tableHeaderDark = Color(0xFF303030); // Dark gray header

  // Table Cell Text Colors
  static const Color tableReadingText = Color(0xFF003087); // Blue for readings
  static const Color tableTopUpText = Color(0xFFFF9800); // Orange for top-ups
  static const Color tableReadingTextDark =
      Color(0xFF64B5F6); // Light blue for readings in dark mode
  static const Color tableTopUpTextDark =
      Color(0xFFFFB74D); // Light orange for top-ups in dark mode
  static const Color tableAverageText =
      Colors.black87; // For average values in light mode
  static const Color tableAverageTextDark =
      Color(0xFFE6E1E5); // For average values in dark mode
  static const Color tableDateText =
      Color(0xFF212121); // For date text in light mode
  static const Color tableDateTextDark =
      Color(0xFFE6E1E5); // For date text in dark mode
  static const Color tableHeaderText =
      Color(0xFF0288D1); // For header text in light mode
  static const Color tableHeaderTextDark =
      Color(0xFF42A5F5); // For header text in dark mode

  // Screen-Specific Colors
  static const Color homeTab = Color(0xFF49D941); // Green
  static const Color costTab = Color(0xFFE65100); // Orange
  static const Color historyTab = Color(0xFF8E24AA); // Purple

  // App Bar Colors - Updated per new color scheme
  static const Color homeAppBarLight =
      Color(0xFF4A90E2); // Soft Blue for dashboard
  static const Color homeAppBarDark =
      Color(0xFF1565C0); // Darker Blue for dashboard in dark mode
  static const Color historyAppBarLight =
      Color(0xFF7E57C2); // Muted Purple for history screen
  static const Color historyAppBarDark =
      Color(0xFF5E35B1); // Darker Purple for history screen in dark mode
  static const Color costAppBarLight =
      Color(0xFFFFA000); // Orange for cost screen
  static const Color costAppBarDark =
      Color(0xFFE65100); // Darker Orange for cost screen in dark mode
  static const Color settingsAppBarLight =
      Color(0xFF546E7A); // Deep Gray for settings screen
  static const Color settingsAppBarDark =
      Color(0xFF37474F); // Darker Gray for settings screen in dark mode
  static const Color notificationAppBarLight =
      Color(0xFF66BB6A); // Vibrant Green for notification screen
  static const Color notificationAppBarDark =
      Color(0xFF388E3C); // Darker Green for notification screen in dark mode

  // Gradient Colors - Updated for new color scheme
  static const List<Color> dashboardMainCardGradient = [
    Color(0xFF4A90E2), // Soft Blue
    Color(0xFFBBDEFB), // Light Blue
  ];
  static const List<Color> dashboardMainCardGradientDark = [
    Color(0xFF1565C0), // Darker Blue
    Color(0xFF64B5F6), // Medium Blue
  ];
  static const List<Color> costMainCardGradient = [
    Color(0xFFFFA000), // Orange
    Color(0xFFFFE0B2), // Light Orange
  ];
  static const List<Color> costMainCardGradientDark = [
    Color(0xFFE65100), // Darker Orange
    Color(0xFFFFB74D), // Medium Orange
  ];
  static const List<Color> notificationMainCardGradient = [
    Color(0xFF66BB6A), // Vibrant Green
    Color(0xFFC8E6C9), // Light Green
  ];
  static const List<Color> notificationMainCardGradientDark = [
    Color(0xFF388E3C), // Darker Green
    Color(0xFF81C784), // Medium Green
  ];
  static const List<Color> historyMainCardGradient = [
    Color(0xFF7E57C2), // Muted Purple
    Color(0xFFD1C4E9), // Light Purple
  ];
  static const List<Color> historyMainCardGradientDark = [
    Color(0xFF5E35B1), // Darker Purple
    Color(0xFF9575CD), // Medium Purple
  ];
  static const List<Color> settingsMainCardGradient = [
    Color(0xFF546E7A), // Deep Gray
    Color(0xFFB0BEC5), // Light Gray
  ];
  static const List<Color> settingsMainCardGradientDark = [
    Color(0xFF37474F), // Darker Gray
    Color(0xFF78909C), // Medium Gray
  ];

  // Form Input Colors
  static const Color inputTextLight =
      Color(0xFF212121); // Dark gray for input text in light mode
  static const Color inputTextDark =
      Color(0xFFFFFFFF); // White for input text in dark mode
  static const Color inputLabelLight =
      Color(0xFF212121); // Dark gray for input labels in light mode
  static const Color inputLabelDark =
      Color(0xFFFFFFFF); // White for input labels in dark mode
  static const Color inputHintLight =
      Color(0xFF757575); // Medium gray for input hints in light mode
  static const Color inputHintDark =
      Color(0xFFB0B0B0); // Light gray for input hints in dark mode
  static const Color inputBorderLight =
      Color(0xFF757575); // Medium gray for input borders in light mode
  static const Color inputBorderDark =
      Color(0xFF757575); // Medium gray for input borders in dark mode
  static const Color inputFocusedBorderLight =
      Color(0xFF0288D1); // Blue for focused input borders in light mode
  static const Color inputFocusedBorderDark =
      Color(0xFF42A5F5); // Light blue for focused input borders in dark mode

  // Message Banner Colors
  static const Color messageBannerLight =
      Color(0xFFF5F5F5); // Light gray for message banner in light mode
  static const Color messageBannerDark =
      Color(0xFF303030); // Dark gray for message banner in dark mode
  static const Color tipBannerLight =
      Color(0xFFE3F2FD); // Light blue for tip banner in light mode
  static const Color tipBannerDark =
      Color(0xFF1E3A8A); // Dark blue for tip banner in dark mode
  static const Color messageTextLight =
      Color(0xDE000000); // Black with 87% opacity for text in light mode
  static const Color messageTextDark =
      Color(0xB3FFFFFF); // White with 70% opacity for text in dark mode

  // Dialog Action Button Colors
  static const Color cancelButtonOutline = Color(0xFF0288D1); // Blue outline
  static const Color deleteButtonBackground =
      Color(0xFFD32F2F); // Red background
  static const Color editButtonBackground =
      Color(0xFFFF9800); // Orange background
  static const Color clearAllButtonBackground =
      Color(0xFFD32F2F); // Red background (same as delete)

  // Button State Colors
  static const Color primaryButtonPressed = Color(0xFF0277BD); // Darker blue
  static const Color deleteButtonPressed = Color(0xFFC62828); // Darker red
  static const Color editButtonPressed = Color(0xFFF57C00); // Darker orange

  // Theme-aware helper methods to replace LekkyColors functionality

  /// Get app bar color for specific screen based on theme
  static Color getAppBarColor(String screen, bool isDark) {
    switch (screen.toLowerCase()) {
      case 'home':
        return isDark ? homeAppBarDark : homeAppBarLight;
      case 'history':
        return isDark ? historyAppBarDark : historyAppBarLight;
      case 'cost':
        return isDark ? costAppBarDark : costAppBarLight;
      case 'settings':
        return isDark ? settingsAppBarDark : settingsAppBarLight;
      case 'notifications':
        return isDark ? notificationAppBarDark : notificationAppBarLight;
      default:
        return isDark ? primaryDark : primary;
    }
  }

  /// Get notification app bar color based on theme
  static Color getNotificationAppBarColor(bool isDark) {
    return isDark ? notificationAppBarDark : notificationAppBarLight;
  }

  /// Get text color for app bar based on screen and theme
  static Color getAppBarTextColor(String screen, bool isDark) {
    switch (screen.toLowerCase()) {
      case 'history':
      case 'cost':
      case 'settings':
        return isDark ? Colors.black : Colors.white;
      case 'home':
      case 'dashboard':
        return isDark ? Colors.black : Colors.white;
      case 'notifications':
      default:
        return Colors.white;
    }
  }

  /// Get filter icon color based on filter visibility and theme
  static Color getFilterIconColor(bool filtersVisible, bool isDark) {
    if (filtersVisible) {
      // Blue when filters are shown
      return isDark ? Colors.blue[300]! : Colors.blue[600]!;
    } else {
      // White when filters are hidden
      return Colors.white;
    }
  }

  /// Get text value color based on theme
  static Color getTextValueColor(bool isDark) {
    return isDark ? tableReadingTextDark : tableReadingText;
  }

  /// Get text currency color based on theme
  static Color getTextCurrencyColor(bool isDark) {
    return isDark ? tableReadingTextDark : tableReadingText;
  }

  /// Get warning indicator color based on theme
  static Color getWarningIndicatorColor(bool isDark) {
    return isDark ? warningDark : warning;
  }

  /// Get error indicator color based on theme
  static Color getErrorIndicatorColor(bool isDark) {
    return isDark ? errorDark : error;
  }

  /// Get add button color based on theme
  static Color getAddButtonColor(bool isDark) {
    return isDark ? successDark : success;
  }

  /// Get dashboard main card gradient based on theme
  static List<Color> getDashboardMainCardGradient(bool isDark) {
    return isDark ? dashboardMainCardGradientDark : dashboardMainCardGradient;
  }

  /// Get cost main card gradient based on theme
  static List<Color> getCostMainCardGradient(bool isDark) {
    return isDark ? costMainCardGradientDark : costMainCardGradient;
  }

  /// Get notification main card gradient based on theme
  static List<Color> getNotificationMainCardGradient(bool isDark) {
    return isDark
        ? notificationMainCardGradientDark
        : notificationMainCardGradient;
  }

  /// Get history main card gradient based on theme
  static List<Color> getHistoryMainCardGradient(bool isDark) {
    return isDark ? historyMainCardGradientDark : historyMainCardGradient;
  }

  /// Get settings main card gradient based on theme
  static List<Color> getSettingsMainCardGradient(bool isDark) {
    return isDark ? settingsMainCardGradientDark : settingsMainCardGradient;
  }

  /// Get dashboard meter card main text color (white in light mode, black in dark mode)
  static Color getDashboardMeterTextColor(bool isDark) {
    return isDark ? const Color(0xFF000000) : const Color(0xFFFFFFFF);
  }

  /// Get dashboard top-up text color (orange in light mode, darker orange in dark mode)
  static Color getDashboardTopUpTextColor(bool isDark) {
    return isDark ? const Color(0xFFE65100) : const Color(0xFFFFA000);
  }

  /// Get recent average chart background color (light orange in light mode, dark gray in dark mode)
  static Color getRecentAverageChartBackground(bool isDark) {
    return isDark ? const Color(0xFF2C2C2E) : const Color(0xFFFFE0B2);
  }

  /// Get notification icon color based on type and theme
  static Color getNotificationIconColor(NotificationType type, bool isDark) {
    switch (type) {
      case NotificationType.lowBalance:
        return isDark ? errorDark : error;
      case NotificationType.timeToTopUp:
        return isDark ? warningDark : warning;
      case NotificationType.invalidRecord:
        return isDark ? warningDark : warning;
      case NotificationType.readingReminder:
        return isDark ? primaryDark : primary;
      case NotificationType.welcome:
        return isDark ? successDark : success;
    }
  }
}
