// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'settings_state.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$SettingsStateImpl _$$SettingsStateImplFromJson(Map<String, dynamic> json) =>
    _$SettingsStateImpl(
      language: json['language'] as String? ?? 'English',
      currency: json['currency'] as String? ?? 'GBP',
      currencySymbol: json['currencySymbol'] as String? ?? '£',
      alertThreshold: (json['alertThreshold'] as num?)?.toDouble() ?? 5.0,
      daysInAdvance: json['daysInAdvance'] as int? ?? 5,
      notificationsEnabled: json['notificationsEnabled'] as bool? ?? true,
      remindersEnabled: json['remindersEnabled'] as bool? ?? true,
      lowBalanceAlertsEnabled:
          json['lowBalanceAlertsEnabled'] as bool? ?? true,
      timeToTopUpAlertsEnabled:
          json['timeToTopUpAlertsEnabled'] as bool? ?? true,
      invalidRecordAlertsEnabled:
          json['invalidRecordAlertsEnabled'] as bool? ?? true,
      reminderFrequency: json['reminderFrequency'] as String? ?? 'weekly',
      reminderStartDateTime: json['reminderStartDateTime'] == null
          ? null
          : DateTime.parse(json['reminderStartDateTime'] as String),
      dateFormat: json['dateFormat'] as String? ?? 'DD-MM-YYYY',
      showTimeWithDate: json['showTimeWithDate'] as bool? ?? false,
      themeMode: $enumDecodeNullable(_$AppThemeModeEnumMap, json['themeMode']) ??
          AppThemeMode.system,
    );

Map<String, dynamic> _$$SettingsStateImplToJson(_$SettingsStateImpl instance) =>
    <String, dynamic>{
      'language': instance.language,
      'currency': instance.currency,
      'currencySymbol': instance.currencySymbol,
      'alertThreshold': instance.alertThreshold,
      'daysInAdvance': instance.daysInAdvance,
      'notificationsEnabled': instance.notificationsEnabled,
      'remindersEnabled': instance.remindersEnabled,
      'lowBalanceAlertsEnabled': instance.lowBalanceAlertsEnabled,
      'timeToTopUpAlertsEnabled': instance.timeToTopUpAlertsEnabled,
      'invalidRecordAlertsEnabled': instance.invalidRecordAlertsEnabled,
      'reminderFrequency': instance.reminderFrequency,
      'reminderStartDateTime': instance.reminderStartDateTime?.toIso8601String(),
      'dateFormat': instance.dateFormat,
      'showTimeWithDate': instance.showTimeWithDate,
      'themeMode': _$AppThemeModeEnumMap[instance.themeMode]!,
    };

const _$AppThemeModeEnumMap = {
  AppThemeMode.light: 'light',
  AppThemeMode.dark: 'dark',
  AppThemeMode.system: 'system',
};