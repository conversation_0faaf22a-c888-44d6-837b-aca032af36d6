// File: lib/core/models/setting.dart
import 'package:equatable/equatable.dart';

/// Model class for application settings
class Setting extends Equatable {
  /// Setting key
  final String key;
  
  /// Setting value
  final String value;

  /// Constructor
  const Setting({
    required this.key,
    required this.value,
  });

  /// Create a copy of this setting with the given fields replaced
  Setting copyWith({
    String? key,
    String? value,
  }) {
    return Setting(
      key: key ?? this.key,
      value: value ?? this.value,
    );
  }

  /// Convert to a map for database operations
  Map<String, dynamic> toMap() {
    return {
      'key': key,
      'value': value,
    };
  }

  /// Create a setting from a map
  factory Setting.fromMap(Map<String, dynamic> map) {
    return Setting(
      key: map['key'] as String,
      value: map['value'] as String,
    );
  }

  @override
  List<Object> get props => [key, value];

  @override
  String toString() {
    return 'Setting(key: $key, value: $value)';
  }
}
