# NewLekky Consolidated Project Plan - Part 4: Data Flow, Calculations, and Validation

This document provides detailed specifications for the data flow, calculation algorithms, and validation rules of the NewLekky app.

## Data Flow and Calculations

### Data Flow

1. **User Input Flow**:
   - User enters meter reading/top-up
   - Entry is validated in real-time
   - If valid, entry is saved to database
   - Averages are recalculated incrementally
   - UI is updated reactively
   - Notifications are evaluated based on new data

2. **Settings Update Flow**:
   - User changes setting
   - Setting is validated
   - Setting is saved to preferences
   - State is updated through provider
   - UI is refreshed reactively
   - Affected features are notified of changes

3. **Notification Flow**:
   - Conditions are evaluated based on current data
   - Notification is scheduled if conditions are met
   - Notification is displayed at scheduled time
   - User action is captured if notification is tapped
   - Appropriate response is triggered based on action

4. **Data Import/Export Flow**:
   - User initiates import/export
   - Data is validated before processing
   - For export: data is formatted and saved to file
   - For import: file is parsed and validated
   - Database is updated with imported data
   - UI is refreshed to reflect changes

### Calculation Logic

1. **Recent Average Calculation**:
   ```
   RecentAverage = ((LastMeterReadingValue + TotalTopUpCount) - NewMeterReadingValue) / DaysBetweenReadings
   ```
   Where:
   - LastMeterReadingValue: The meter reading from the previous entry
   - TotalTopUpCount: Sum of all top-ups between previous and current readings
   - NewMeterReadingValue: The most recent meter reading
   - DaysBetweenReadings: Number of days between previous and current readings (accurate to the minute level)

2. **Total Average Calculation**:
   ```
   TotalAverage = ((first reading + all top ups before current row) - current row reading) / days between first and current
   ```
   Where:
   - First reading: The first meter reading in the database
   - All top ups before current row: Sum of all top-ups since the first reading
   - Current row reading: The meter reading being evaluated
   - Days between first and current: Number of days between first and current readings (using seconds-based precision)

3. **Days Remaining Calculation**:
   ```
   DaysRemaining = CurrentReading / RecentAverageDailyUsage
   ```
   Where:
   - CurrentReading: The most recent meter reading
   - RecentAverageDailyUsage: The recent average daily usage

4. **Validity Check**:
   ```
   IsValid = CurrentReading <= (PreviousReading + TopUps)
   ```
   Where:
   - CurrentReading: The meter reading being validated (monetary value rounded to 2 decimal places)
   - PreviousReading: The meter reading from the previous entry
   - TopUps: Sum of all top-ups between previous and current readings

   Invalid entries are identified by checking entries that come after and displaying warning triangles

5. **Seasonal Adjustment Calculation**:
   ```
   SeasonalFactor = AverageUsageInSameMonthPreviousYears / AverageYearlyUsage
   AdjustedProjection = StandardProjection * SeasonalFactor
   ```
   Where:
   - AverageUsageInSameMonthPreviousYears: Average daily usage in the same month in previous years
   - AverageYearlyUsage: Average daily usage across the entire year
   - StandardProjection: Projection based on recent average without seasonal adjustment

6. **Confidence Interval Calculation**:
   ```
   ConfidenceInterval = RecentAverage ± (StandardDeviation * 1.28)
   ```
   Where:
   - RecentAverage: The recent average daily usage
   - StandardDeviation: Standard deviation of daily usage over recent period
   - 1.28: Z-score for 80% confidence interval

## Validation Rules

### Meter Reading Validation

1. **Required Fields**:
   - Value: Must be provided
   - Date: Must be provided

2. **Value Validation**:
   - Must be a positive number (≥ 0)
   - Must be numeric with up to 2 decimal places
   - Cannot exceed previous reading plus any top-ups (IsValid check)
   - Should be within reasonable range (warning if > 2x recent average)

3. **Date Validation**:
   - Must be a valid date
   - Cannot be in the future
   - Must be chronological (not before previous reading)
   - Cannot duplicate an existing reading date

4. **Contextual Validation**:
   - If first reading, no additional validation needed
   - If subsequent reading, must have at least one previous reading
   - If value is significantly different from expected, show warning

### Top-Up Validation

1. **Required Fields**:
   - Amount: Must be provided
   - Date: Must be provided

2. **Amount Validation**:
   - Must be a positive number (> 0)
   - Must be numeric with up to 2 decimal places
   - Should be within reasonable range (warning if unusually large)

3. **Date Validation**:
   - Must be a valid date
   - Cannot be in the future
   - Must be chronological (not before first reading)
   - Can be same date as a reading or other top-up

4. **Contextual Validation**:
   - Must have at least one reading before adding top-up
   - If amount is significantly different from usual, show warning

### General Validation

1. **Duplicate Prevention**:
   - No duplicate timestamps for same entry type
   - If exact timestamp exists, suggest microsecond adjustment

2. **Format Validation**:
   - Dates must be in valid format according to user's locale
   - Currency values must follow locale-specific formatting

3. **Logical Validation**:
   - Entries must maintain logical sequence
   - Total usage cannot be negative
   - Readings should generally decrease over time

## Data Validation UI/UX

### Real-Time Validation

1. **Input Field Validation**:
   - Validate as user types with immediate feedback
   - Show error messages below input fields
   - Use color coding to indicate validation status
   - Disable save button until all validation passes

2. **Error Message Guidelines**:
   - Be specific about the error
   - Provide actionable guidance for correction
   - Use friendly, non-technical language
   - Include visual indicators (icons)

3. **Warning vs. Error**:
   - Errors prevent saving (hard validation)
   - Warnings allow saving with confirmation (soft validation)
   - Use different colors and icons to distinguish

### Validation Dashboard

1. **Invalid Entries View**:
   - List all entries with validation issues
   - Sort by severity and date
   - Show specific validation error for each entry
   - Provide quick fix options where possible

2. **Batch Correction**:
   - Allow selection of multiple entries
   - Provide common correction actions
   - Show preview of changes before applying
   - Confirm successful corrections

3. **Data Integrity Check**:
   - Periodic validation of entire database
   - Report on overall data health
   - Identify patterns of issues
   - Suggest comprehensive fixes

## Data Repair Tools

1. **Auto-Correction Options**:
   - Timestamp adjustments for near-duplicates
   - Rounding for decimal precision issues
   - Date format standardization
   - Simple validation fixes

2. **Manual Repair Tools**:
   - Edit individual entries
   - Delete problematic entries
   - Add missing entries
   - Adjust entry sequences

3. **Repair Wizards**:
   - Step-by-step guidance for fixing complex issues
   - Batch processing for similar issues
   - Contextual help during repair process
   - Validation after repairs

4. **Data Recovery**:
   - Restore from backup
   - Import from external source
   - Reconstruct missing data
   - Rollback recent changes

## Implementation Approach

The validation system will be implemented as a multi-layered approach:

1. **UI Layer Validation**:
   - Immediate feedback during input
   - Visual indicators and error messages
   - Prevention of invalid submissions

2. **Domain Layer Validation**:
   - Business rule enforcement
   - Contextual validation with existing data
   - Validation service for centralized rules

3. **Data Layer Validation**:
   - Database constraints
   - Data integrity checks
   - Migration validation

This comprehensive approach ensures data integrity while providing a user-friendly experience with clear guidance for correcting issues.
