import 'package:flutter/material.dart';

/// A widget for section headers in the setup screen
class SetupSectionHeader extends StatelessWidget {
  /// Title of the section
  final String title;
  
  /// Description of the section
  final String? description;
  
  /// Icon to display
  final IconData? icon;
  
  /// Constructor
  const SetupSectionHeader({
    Key? key,
    required this.title,
    this.description,
    this.icon,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            if (icon != null) ...[
              Icon(
                icon,
                color: Theme.of(context).colorScheme.primary,
                size: 24,
              ),
              const SizedBox(width: 8),
            ],
            Text(
              title,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
          ],
        ),
        if (description != null) ...[
          const SizedBox(height: 4),
          Text(
            description!,
            style: TextStyle(
              fontSize: 14,
              color: Theme.of(context).textTheme.bodySmall?.color,
            ),
          ),
        ],
        const SizedBox(height: 16),
      ],
    );
  }
}
