import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/providers/settings_provider.dart';
import '../../../../features/setup/presentation/widgets/appearance_settings_card.dart';
import '../../../../core/shared/models/theme_mode.dart';

/// Theme Mode settings screen
class ThemeModeScreen extends ConsumerWidget {
  /// Constructor
  const ThemeModeScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Theme Mode'),
        backgroundColor: const Color(0xFF424242),
        foregroundColor: Colors.white,
      ),
      body: ref.watch(settingsProvider).when(
            loading: () => const Center(child: CircularProgressIndicator()),
            error: (error, stack) => Center(child: Text('Error: $error')),
            data: (settings) {
              return SingleChildScrollView(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Appearance settings card
                    AppearanceSettingsCard(
                      themeMode: settings.themeMode,
                      onThemeModeChanged: (mode) {
                        ref
                            .read(settingsProvider.notifier)
                            .updateThemeMode(mode);
                      },
                    ),
                  ],
                ),
              );
            },
          ),
    );
  }
}
