import 'package:flutter/material.dart';

/// A card widget for settings sections with toggle functionality
class SettingsSectionCard extends StatelessWidget {
  /// Title of the section
  final String title;

  /// Icon to display
  final IconData icon;

  /// Color of the icon
  final Color iconColor;

  /// Whether the section is enabled
  final bool isEnabled;

  /// Callback when toggle is pressed
  final Function(bool) onToggle;

  /// Callback when card is tapped
  final VoidCallback? onTap;

  /// Optional subtitle to display
  final String? subtitle;

  /// Optional current value to display
  final String? currentValue;

  /// Optional child widgets to display when expanded
  final List<Widget>? children;

  /// Constructor
  const SettingsSectionCard({
    Key? key,
    required this.title,
    required this.icon,
    required this.iconColor,
    required this.isEnabled,
    required this.onToggle,
    this.onTap,
    this.subtitle,
    this.currentValue,
    this.children,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final cardColor = isDarkMode ? const Color(0xFF1E1E1E) : Colors.white;
    final textColor = isDarkMode ? Colors.white : Colors.black87;
    final subtitleColor = isDarkMode ? Colors.white70 : Colors.black54;

    return Card(
      margin: const EdgeInsets.only(bottom: 8.0),
      color: cardColor,
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.0),
      ),
      child: Column(
        children: [
          InkWell(
            onTap: onTap,
            borderRadius: BorderRadius.circular(12.0),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        icon,
                        color: iconColor,
                        size: 24,
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              title,
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.w500,
                                color: textColor,
                              ),
                            ),
                            if (currentValue != null) ...[
                              const SizedBox(height: 4),
                              Text(
                                currentValue!,
                                style: TextStyle(
                                  fontSize: 14,
                                  color: subtitleColor,
                                ),
                              ),
                            ],
                          ],
                        ),
                      ),
                      Switch(
                        value: isEnabled,
                        onChanged: onToggle,
                        activeColor: Colors.blue,
                        activeTrackColor: Colors.blue.withOpacity(0.5),
                        inactiveThumbColor: Colors.grey,
                        inactiveTrackColor: Colors.grey.withOpacity(0.5),
                      ),
                    ],
                  ),
                  if (subtitle != null) ...[
                    const SizedBox(height: 8),
                    Text(
                      subtitle!,
                      style: TextStyle(
                        fontSize: 14,
                        color: subtitleColor,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
          if (isEnabled && children != null && children!.isNotEmpty)
            Padding(
              padding: const EdgeInsets.only(
                left: 16.0,
                right: 16.0,
                bottom: 16.0,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: children!,
              ),
            ),
        ],
      ),
    );
  }
}
