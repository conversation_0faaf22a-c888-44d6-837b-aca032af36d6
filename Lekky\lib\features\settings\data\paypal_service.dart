import 'package:flutter/material.dart';
import 'package:flutter_paypal/flutter_paypal.dart';
import '../../../core/utils/logger.dart';

/// Logger instance for PayPal service
final _logger = Logger('PayPal');

/// Service for handling PayPal donations
class PayPalService {
  /// PayPal Client ID
  static const String clientId =
      'AeJ8SgEUo-UKBlhW3GS_BTD0uMz8jzFnc08c5N9SfBA344bYfu61uvpDdmP4_O3hRAKql7e-Lr8hrKW-';

  /// Display App Name
  static const String appName = 'Lekky';

  /// Whether to use sandbox mode
  static const bool sandboxMode = true;

  /// Return URL
  static const String returnURL = 'return.example.com';

  /// Cancel URL
  static const String cancelURL = 'cancel.example.com';

  /// Donation description
  static const String description = 'Support Lekky App';

  /// Available donation amounts
  static const List<String> donationAmounts = [
    '1.00',
    '2.00',
    '5.00',
    '10.00',
    '20.00',
    '50.00'
  ];

  /// Process a donation
  static Future<bool> processDonation(
      BuildContext context, String amount, String currency) async {
    try {
      _logger.i('PayPalService: Processing donation of $amount $currency');

      // Store context for later use
      final BuildContext originalContext = context;

      await Navigator.of(context).push(
        MaterialPageRoute(
          builder: (BuildContext context) => UsePaypal(
            sandboxMode: sandboxMode,
            clientId: clientId,
            secretKey:
                'EJzKs247gspBVdardCYI7Sc1ggH7nFuA3_XDPhIO7WVpAx3jmuiqx74KnJhV-3gYdFjkoYfP4ePgNPFI',
            returnURL: returnURL,
            cancelURL: cancelURL,
            transactions: [
              {
                'amount': {
                  'total': amount,
                  'currency': currency,
                },
                'description': description,
              }
            ],
            note: 'Thank you for your donation!',
            onSuccess: (Map params) {
              _logger.i('PayPalService: Donation successful', details: params);
              // Check if the context is still valid
              if (originalContext.mounted) {
                _showThankYouDialog(originalContext);
              }
            },
            onError: (error) {
              _logger.e('PayPalService: Donation error', details: error);
              // Check if the context is still valid
              if (originalContext.mounted) {
                _showErrorDialog(originalContext, error.toString());
              }
            },
            onCancel: (params) {
              _logger.w('PayPalService: Donation cancelled', details: params);
            },
          ),
        ),
      );

      return true;
    } catch (e) {
      _logger.e('PayPalService: Error processing donation',
          details: e.toString());
      // Check if the context is still valid
      if (context.mounted) {
        _showErrorDialog(context, e.toString());
      }
      return false;
    }
  }

  /// Show thank you dialog
  static void _showThankYouDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Thank You!'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.favorite,
              color: Colors.red,
              size: 48,
            ),
            SizedBox(height: 16),
            Text(
              'Thank you for your donation! Your support helps us continue to improve the app.',
              textAlign: TextAlign.center,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  /// Show error dialog
  static void _showErrorDialog(BuildContext context, String error) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Donation Error'),
        content: Text('There was an error processing your donation: $error'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}
