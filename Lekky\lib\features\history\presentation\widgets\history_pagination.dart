import 'package:flutter/material.dart';

/// A widget that displays pagination controls for the History screen
class HistoryPagination extends StatelessWidget {
  /// The current page
  final int currentPage;
  
  /// The total number of pages
  final int totalPages;
  
  /// Callback when the previous page button is pressed
  final VoidCallback onPreviousPage;
  
  /// Callback when the next page button is pressed
  final VoidCallback onNextPage;
  
  /// Callback when a specific page is selected
  final ValueChanged<int> onPageSelected;

  /// Constructor
  const HistoryPagination({
    Key? key,
    required this.currentPage,
    required this.totalPages,
    required this.onPreviousPage,
    required this.onNextPage,
    required this.onPageSelected,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Page info
          Text(
            'Page ${currentPage + 1} of $totalPages',
            style: TextStyle(
              fontSize: 14,
              color: theme.colorScheme.onSurface,
            ),
          ),
          
          // Pagination controls
          Row(
            children: [
              // Previous page button
              IconButton(
                icon: const Icon(Icons.chevron_left),
                onPressed: currentPage > 0 ? onPreviousPage : null,
                tooltip: 'Previous page',
              ),
              
              // Page buttons
              ..._buildPageButtons(context),
              
              // Next page button
              IconButton(
                icon: const Icon(Icons.chevron_right),
                onPressed: currentPage < totalPages - 1 ? onNextPage : null,
                tooltip: 'Next page',
              ),
            ],
          ),
        ],
      ),
    );
  }
  
  /// Build the page buttons
  List<Widget> _buildPageButtons(BuildContext context) {
    final theme = Theme.of(context);
    final List<Widget> pageButtons = [];
    
    // Determine which page buttons to show
    final int maxVisiblePages = 5;
    int startPage = 0;
    int endPage = totalPages - 1;
    
    if (totalPages > maxVisiblePages) {
      // Show a subset of pages with the current page in the middle
      final int halfVisible = maxVisiblePages ~/ 2;
      startPage = currentPage - halfVisible;
      endPage = currentPage + halfVisible;
      
      // Adjust if out of bounds
      if (startPage < 0) {
        endPage += -startPage;
        startPage = 0;
      }
      
      if (endPage >= totalPages) {
        startPage -= (endPage - totalPages + 1);
        endPage = totalPages - 1;
      }
      
      // Ensure startPage is not negative
      startPage = startPage < 0 ? 0 : startPage;
    }
    
    // Add first page button if not visible
    if (startPage > 0) {
      pageButtons.add(
        _buildPageButton(context, 0),
      );
      
      // Add ellipsis if there's a gap
      if (startPage > 1) {
        pageButtons.add(
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8),
            child: Text(
              '...',
              style: TextStyle(
                color: theme.colorScheme.onSurface,
              ),
            ),
          ),
        );
      }
    }
    
    // Add visible page buttons
    for (int i = startPage; i <= endPage; i++) {
      pageButtons.add(
        _buildPageButton(context, i),
      );
    }
    
    // Add last page button if not visible
    if (endPage < totalPages - 1) {
      // Add ellipsis if there's a gap
      if (endPage < totalPages - 2) {
        pageButtons.add(
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8),
            child: Text(
              '...',
              style: TextStyle(
                color: theme.colorScheme.onSurface,
              ),
            ),
          ),
        );
      }
      
      pageButtons.add(
        _buildPageButton(context, totalPages - 1),
      );
    }
    
    return pageButtons;
  }
  
  /// Build a single page button
  Widget _buildPageButton(BuildContext context, int page) {
    final theme = Theme.of(context);
    final bool isCurrentPage = page == currentPage;
    
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 4),
      child: InkWell(
        onTap: isCurrentPage ? null : () => onPageSelected(page),
        borderRadius: BorderRadius.circular(4),
        child: Container(
          width: 32,
          height: 32,
          alignment: Alignment.center,
          decoration: BoxDecoration(
            color: isCurrentPage ? theme.colorScheme.primary : Colors.transparent,
            borderRadius: BorderRadius.circular(4),
            border: Border.all(
              color: isCurrentPage 
                  ? theme.colorScheme.primary 
                  : theme.colorScheme.outline.withOpacity(0.5),
            ),
          ),
          child: Text(
            '${page + 1}',
            style: TextStyle(
              color: isCurrentPage 
                  ? theme.colorScheme.onPrimary 
                  : theme.colorScheme.onSurface,
              fontWeight: isCurrentPage ? FontWeight.bold : FontWeight.normal,
            ),
          ),
        ),
      ),
    );
  }
}
