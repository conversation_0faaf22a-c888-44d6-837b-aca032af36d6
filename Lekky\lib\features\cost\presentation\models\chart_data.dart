// File: lib/features/cost/presentation/models/chart_data.dart

/// Represents a data point for cost charts
class ChartData {
  /// Date of the data point
  final DateTime date;
  
  /// Usage value for this data point
  final double usage;
  
  /// Cost value for this data point
  final double cost;

  /// Constructor
  const ChartData({
    required this.date,
    required this.usage,
    required this.cost,
  });

  /// Create a copy with updated values
  ChartData copyWith({
    DateTime? date,
    double? usage,
    double? cost,
  }) {
    return ChartData(
      date: date ?? this.date,
      usage: usage ?? this.usage,
      cost: cost ?? this.cost,
    );
  }

  @override
  String toString() {
    return 'ChartData(date: $date, usage: $usage, cost: $cost)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ChartData &&
        other.date == date &&
        other.usage == usage &&
        other.cost == cost;
  }

  @override
  int get hashCode {
    return date.hashCode ^ usage.hashCode ^ cost.hashCode;
  }
}
