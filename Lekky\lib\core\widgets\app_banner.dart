import 'package:flutter/material.dart';
import '../theme/app_colors.dart';

/// A reusable banner widget that displays a message with proper theming
///
/// This is the primary banner component for the app. All other banner types
/// have been removed as per requirements.
class AppBanner extends StatelessWidget {
  /// The message to display
  final String message;

  /// The background color of the banner (used when gradient is null)
  final Color backgroundColor;

  /// The gradient colors for the banner (optional)
  final List<Color>? gradientColors;

  /// The text color of the banner
  final Color textColor;

  /// The icon to display (optional)
  final IconData? icon;

  /// Optional onDismiss callback
  final VoidCallback? onDismiss;

  /// Optional border radius for the banner
  final BorderRadius? borderRadius;

  /// Creates a banner
  const AppBanner({
    super.key,
    required this.message,
    this.backgroundColor =
        AppColors.homeAppBarLight, // Default blue background from screenshot
    this.gradientColors,
    this.textColor = Colors.white, // Default white text from screenshot
    this.icon,
    this.onDismiss,
    this.borderRadius,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      color: gradientColors != null ? Colors.transparent : backgroundColor,
      elevation: 0,
      borderRadius: borderRadius,
      child: Container(
        decoration: gradientColors != null
            ? BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: gradientColors!,
                ),
                borderRadius: borderRadius,
              )
            : borderRadius != null
                ? BoxDecoration(
                    color: backgroundColor,
                    borderRadius: borderRadius,
                  )
                : null,
        child: SafeArea(
          child: Container(
            width: double.infinity, // Full width banner as shown in screenshot
            padding: EdgeInsets.symmetric(
              horizontal: 16.0,
              vertical: onDismiss != null
                  ? 8.0
                  : 12.0, // Reduced padding for dialog context
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                if (icon != null) ...[
                  Icon(
                    icon,
                    color: textColor,
                    size: 20,
                  ),
                  const SizedBox(width: 12),
                ],
                Expanded(
                  child: Text(
                    message,
                    style: TextStyle(
                      color: textColor,
                      fontSize: onDismiss != null
                          ? 20.0 // Smaller font size for dialog context
                          : 28.0, // Larger font size for app title as shown in screenshot
                      fontWeight: FontWeight
                          .w500, // Medium weight for better visibility
                      height: 1.2, // Improved text readability
                    ),
                    overflow: TextOverflow
                        .ellipsis, // Truncate with ellipsis if needed
                    softWrap: true,
                    maxLines: 1, // Limit to 1 line for app title
                  ),
                ),
                if (onDismiss != null) ...[
                  SizedBox(
                    width: 40,
                    height: 40,
                    child: IconButton(
                      icon: Icon(
                        Icons.close,
                        color: textColor,
                        size: 18,
                      ),
                      padding: EdgeInsets.zero,
                      onPressed: onDismiss,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }
}
