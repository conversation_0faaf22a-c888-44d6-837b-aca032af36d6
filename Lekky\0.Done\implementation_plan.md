# Implementation Plan for NewLekky Splash and Welcome Screens

## Project Structure

```
NewLekky/
├── lib/
│   ├── core/
│   │   ├── constants/
│   │   │   ├── app_colors.dart
│   │   │   └── app_strings.dart
│   │   ├── theme/
│   │   │   └── app_theme.dart
│   │   └── utils/
│   │       ├── app_initializer.dart
│   │       └── permission_handler.dart
│   ├── features/
│   │   ├── splash/
│   │   │   ├── presentation/
│   │   │   │   ├── screens/
│   │   │   │   │   └── splash_screen.dart
│   │   │   │   └── controllers/
│   │   │   │       └── splash_controller.dart
│   │   │   └── domain/
│   │   │       └── usecases/
│   │   │           ├── check_first_time_user.dart
│   │   │           └── initialize_app.dart
│   │   └── welcome/
│   │       ├── presentation/
│   │       │   ├── screens/
│   │       │   │   └── welcome_screen.dart
│   │       │   ├── widgets/
│   │       │   │   ├── feature_item.dart
│   │       │   │   └── welcome_buttons.dart
│   │       │   └── controllers/
│   │       │       └── welcome_controller.dart
│   │       └── domain/
│   │           └── usecases/
│   │               ├── mark_welcome_completed.dart
│   │               └── restore_data.dart
│   ├── main.dart
│   └── app.dart
├── assets/
│   ├── images/
│   │   ├── lekky_logo.png
│   │   └── lekky_icon.png
│   └── fonts/
└── pubspec.yaml
```

## Implementation Steps

### 1. Project Setup

1. Create a new Flutter project
   ```bash
   flutter create new_lekky
   ```

2. Set up project structure
   - Create directories as outlined above
   - Configure pubspec.yaml with required dependencies

3. Add assets
   - Create logo and icon assets
   - Configure pubspec.yaml to include assets

### 2. Core Components

1. Create app colors constants
   ```dart
   // app_colors.dart
   import 'package:flutter/material.dart';

   class AppColors {
     static const primaryBlue = Color(0xFF003087);
     static const secondaryBlue = Color(0xFF1A5DB4);
     static const accentBlue = Color(0xFF1A75FF);
     static const white = Colors.white;
     static const black = Colors.black;
   }
   ```

2. Create app strings constants
   ```dart
   // app_strings.dart
   class AppStrings {
     // Splash Screen
     static const appName = 'Lekky';
     static const tagline = 'Your Prepaid Meter Assistant';
     static const splashQuote = 'I\'m not cheap—I\'m kilowatt-conscious.';
     static const checkingPermissions = 'Checking permissions...';
     
     // Welcome Screen
     static const welcomeTitle = 'Welcome to Lekky';
     static const welcomeSubtitle = 'Your personal prepaid meter assistant';
     
     // Feature Titles
     static const trackUsage = 'Track Your Usage';
     static const getAlerts = 'Get Timely Alerts';
     static const viewHistory = 'View History';
     static const calculateCosts = 'Calculate Costs';
     
     // Feature Descriptions
     static const trackUsageDesc = 'Monitor your electricity consumption and spending';
     static const getAlertsDesc = 'Receive notifications when your balance is running low';
     static const viewHistoryDesc = 'See your past meter readings and top-ups';
     static const calculateCostsDesc = 'Estimate your electricity costs over different periods';
     
     // Buttons
     static const getStarted = 'Get Started';
     static const restoreData = 'Restore Previous Data';
     static const restoreHelper = 'Have a backup from another device?';
   }
   ```

3. Create app theme
   ```dart
   // app_theme.dart
   import 'package:flutter/material.dart';
   import '../constants/app_colors.dart';

   class AppTheme {
     static ThemeData get lightTheme {
       return ThemeData(
         primaryColor: AppColors.primaryBlue,
         scaffoldBackgroundColor: AppColors.primaryBlue,
         fontFamily: 'Roboto',
         textTheme: TextTheme(
           headline1: TextStyle(
             color: AppColors.white,
             fontSize: 36,
             fontWeight: FontWeight.bold,
           ),
           headline2: TextStyle(
             color: AppColors.white,
             fontSize: 28,
             fontWeight: FontWeight.bold,
           ),
           headline3: TextStyle(
             color: AppColors.white,
             fontSize: 18,
             fontWeight: FontWeight.bold,
           ),
           bodyText1: TextStyle(
             color: AppColors.white,
             fontSize: 16,
           ),
           bodyText2: TextStyle(
             color: AppColors.white.withOpacity(0.85),
             fontSize: 14,
           ),
         ),
         elevatedButtonTheme: ElevatedButtonThemeData(
           style: ElevatedButton.styleFrom(
             primary: AppColors.accentBlue,
             onPrimary: AppColors.white,
             padding: EdgeInsets.symmetric(vertical: 16, horizontal: 24),
             shape: RoundedRectangleBorder(
               borderRadius: BorderRadius.circular(8),
             ),
           ),
         ),
         outlinedButtonTheme: OutlinedButtonThemeData(
           style: OutlinedButton.styleFrom(
             primary: AppColors.white,
             side: BorderSide(color: AppColors.white),
             padding: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
             shape: RoundedRectangleBorder(
               borderRadius: BorderRadius.circular(8),
             ),
           ),
         ),
       );
     }

     static ThemeData get darkTheme {
       // For future implementation
       return lightTheme;
     }
   }
   ```

4. Create app initializer
   ```dart
   // app_initializer.dart
   import 'package:shared_preferences/shared_preferences.dart';

   class AppInitializer {
     static Future<bool> initialize() async {
       // Initialize services, database, etc.
       await Future.delayed(Duration(seconds: 2)); // Simulate initialization
       return true;
     }
     
     static Future<bool> isFirstTimeUser() async {
       final prefs = await SharedPreferences.getInstance();
       return !(prefs.getBool('has_seen_welcome') ?? false);
     }
     
     static Future<void> markWelcomeCompleted() async {
       final prefs = await SharedPreferences.getInstance();
       await prefs.setBool('has_seen_welcome', true);
     }
   }
   ```

### 3. Splash Screen Implementation

1. Create splash controller
   ```dart
   // splash_controller.dart
   import 'package:flutter/material.dart';
   import '../../../core/utils/app_initializer.dart';

   class SplashController extends ChangeNotifier {
     String _statusText = "Initializing...";
     bool _isInitialized = false;
     
     String get statusText => _statusText;
     bool get isInitialized => _isInitialized;
     
     Future<void> initializeApp() async {
       _updateStatus("Initializing...");
       await Future.delayed(Duration(seconds: 1));
       
       _updateStatus("Checking permissions...");
       await Future.delayed(Duration(seconds: 1));
       
       final initialized = await AppInitializer.initialize();
       _isInitialized = initialized;
       notifyListeners();
     }
     
     Future<bool> isFirstTimeUser() async {
       return AppInitializer.isFirstTimeUser();
     }
     
     void _updateStatus(String status) {
       _statusText = status;
       notifyListeners();
     }
   }
   ```

2. Create splash screen
   ```dart
   // splash_screen.dart
   import 'package:flutter/material.dart';
   import 'package:provider/provider.dart';
   import '../controllers/splash_controller.dart';
   import '../../../core/constants/app_colors.dart';
   import '../../../core/constants/app_strings.dart';
   import '../../welcome/presentation/screens/welcome_screen.dart';
   import '../../../home/<USER>/screens/home_screen.dart';

   class SplashScreen extends StatefulWidget {
     @override
     _SplashScreenState createState() => _SplashScreenState();
   }

   class _SplashScreenState extends State<SplashScreen> with SingleTickerProviderStateMixin {
     late AnimationController _animationController;
     late Animation<double> _fadeAnimation;
     late SplashController _controller;
     
     @override
     void initState() {
       super.initState();
       _setupAnimations();
       _controller = Provider.of<SplashController>(context, listen: false);
       _initializeApp();
     }
     
     void _setupAnimations() {
       _animationController = AnimationController(
         vsync: this,
         duration: const Duration(milliseconds: 1500),
       );
       
       _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
         CurvedAnimation(
           parent: _animationController,
           curve: Interval(0.0, 0.5, curve: Curves.easeIn),
         ),
       );
       
       _animationController.forward();
     }
     
     Future<void> _initializeApp() async {
       await _controller.initializeApp();
       
       // Ensure minimum display time
       await Future.delayed(const Duration(seconds: 1));
       
       // Navigate to appropriate screen
       final isFirstTimeUser = await _controller.isFirstTimeUser();
       
       if (isFirstTimeUser) {
         Navigator.of(context).pushReplacement(
           MaterialPageRoute(builder: (_) => WelcomeScreen()),
         );
       } else {
         Navigator.of(context).pushReplacement(
           MaterialPageRoute(builder: (_) => HomeScreen()),
         );
       }
     }
     
     @override
     void dispose() {
       _animationController.dispose();
       super.dispose();
     }
     
     @override
     Widget build(BuildContext context) {
       return Scaffold(
         backgroundColor: AppColors.primaryBlue,
         body: Consumer<SplashController>(
           builder: (context, controller, child) {
             return FadeTransition(
               opacity: _fadeAnimation,
               child: Center(
                 child: Column(
                   mainAxisAlignment: MainAxisAlignment.center,
                   children: [
                     const SizedBox(height: 80),
                     Image.asset(
                       'assets/images/lekky_logo.png',
                       width: 100,
                       height: 100,
                     ),
                     const SizedBox(height: 24),
                     Text(
                       AppStrings.appName,
                       style: Theme.of(context).textTheme.headline1,
                     ),
                     const SizedBox(height: 8),
                     Text(
                       AppStrings.tagline,
                       style: Theme.of(context).textTheme.bodyText1,
                     ),
                     const SizedBox(height: 32),
                     Container(
                       margin: const EdgeInsets.symmetric(horizontal: 40),
                       padding: const EdgeInsets.all(16),
                       decoration: BoxDecoration(
                         color: Colors.white.withOpacity(0.2),
                         borderRadius: BorderRadius.circular(12),
                       ),
                       child: Text(
                         AppStrings.splashQuote,
                         textAlign: TextAlign.center,
                         style: TextStyle(
                           color: Colors.white,
                           fontSize: 16,
                           fontStyle: FontStyle.italic,
                         ),
                       ),
                     ),
                     const SizedBox(height: 40),
                     CircularProgressIndicator(
                       valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                     ),
                     const SizedBox(height: 24),
                     Text(
                       controller.statusText,
                       style: Theme.of(context).textTheme.bodyText2,
                     ),
                   ],
                 ),
               ),
             );
           }
         ),
       );
     }
   }
   ```
