import 'package:flutter/material.dart';
import '../theme/app_colors.dart';

/// Button types for different actions
enum LekkyButtonType {
  /// Primary actions (Save, Apply) - Blue filled
  primary,

  /// Secondary actions (Cancel, Close) - Blue outlined
  secondary,

  /// Destructive actions (Delete, Clear All) - Red filled
  destructive,

  /// Special actions (Edit Entry) - Orange filled
  special,

  /// Success actions (Import, Restore) - Green filled
  success,

  /// Info actions (Email, Feedback) - Blue filled
  info,
}

/// Button sizes for different contexts
enum LekkyButtonSize {
  /// Standard size for most contexts
  standard,

  /// Compact size for dialogs with space constraints
  compact,

  /// Full width for mobile dialogs
  fullWidth,
}

/// Standardized button component following Lekky design templates
class LekkyButton extends StatelessWidget {
  /// Button text
  final String text;

  /// Callback when button is pressed
  final VoidCallback? onPressed;

  /// Button type determining appearance
  final LekkyButtonType type;

  /// Button size variant
  final LekkyButtonSize size;

  /// Whether button is in loading state
  final bool isLoading;

  /// Custom text style for the button text
  final TextStyle? textStyle;

  /// Constructor
  const LekkyButton({
    super.key,
    required this.text,
    this.onPressed,
    this.type = LekkyButtonType.primary,
    this.size = LekkyButtonSize.standard,
    this.isLoading = false,
    this.textStyle,
  });

  @override
  Widget build(BuildContext context) {
    final isEnabled = onPressed != null && !isLoading;

    // Get button styling based on type
    final buttonStyle = _getButtonStyle(context, isEnabled);

    // Get button content
    final buttonChild = _getButtonChild();

    // Apply size constraints
    final sizedButton =
        _applySizeConstraints(context, buttonStyle, buttonChild);

    return sizedButton;
  }

  /// Get button style based on type and state
  ButtonStyle _getButtonStyle(BuildContext context, bool isEnabled) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    switch (type) {
      case LekkyButtonType.primary:
        return ElevatedButton.styleFrom(
          backgroundColor: isEnabled
              ? AppColors.primary
              : AppColors.primary.withOpacity(0.5),
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          elevation: isEnabled ? 2 : 0,
        ).copyWith(
          backgroundColor: MaterialStateProperty.resolveWith((states) {
            if (states.contains(MaterialState.pressed)) {
              return AppColors.primaryButtonPressed;
            }
            if (!isEnabled) {
              return AppColors.primary.withOpacity(0.5);
            }
            return AppColors.primary;
          }),
        );

      case LekkyButtonType.secondary:
        return OutlinedButton.styleFrom(
          foregroundColor: isEnabled
              ? AppColors.cancelButtonOutline
              : AppColors.cancelButtonOutline.withOpacity(0.5),
          side: BorderSide(
            color: isEnabled
                ? AppColors.cancelButtonOutline
                : AppColors.cancelButtonOutline.withOpacity(0.5),
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          backgroundColor: Colors.transparent,
        ).copyWith(
          backgroundColor: MaterialStateProperty.resolveWith((states) {
            if (states.contains(MaterialState.pressed)) {
              return AppColors.cancelButtonOutline.withOpacity(0.1);
            }
            return Colors.transparent;
          }),
        );

      case LekkyButtonType.destructive:
        return ElevatedButton.styleFrom(
          backgroundColor: isEnabled
              ? AppColors.deleteButtonBackground
              : AppColors.deleteButtonBackground.withOpacity(0.5),
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          elevation: isEnabled ? 2 : 0,
        ).copyWith(
          backgroundColor: MaterialStateProperty.resolveWith((states) {
            if (states.contains(MaterialState.pressed)) {
              return AppColors.deleteButtonPressed;
            }
            if (!isEnabled) {
              return AppColors.deleteButtonBackground.withOpacity(0.5);
            }
            return AppColors.deleteButtonBackground;
          }),
        );

      case LekkyButtonType.special:
        return ElevatedButton.styleFrom(
          backgroundColor: isEnabled
              ? AppColors.editButtonBackground
              : AppColors.editButtonBackground.withOpacity(0.5),
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          elevation: isEnabled ? 2 : 0,
        ).copyWith(
          backgroundColor: MaterialStateProperty.resolveWith((states) {
            if (states.contains(MaterialState.pressed)) {
              return AppColors.editButtonPressed;
            }
            if (!isEnabled) {
              return AppColors.editButtonBackground.withOpacity(0.5);
            }
            return AppColors.editButtonBackground;
          }),
        );

      case LekkyButtonType.success:
        final successColor = isDark ? AppColors.successDark : AppColors.success;
        return ElevatedButton.styleFrom(
          backgroundColor:
              isEnabled ? successColor : successColor.withOpacity(0.5),
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          elevation: isEnabled ? 2 : 0,
        ).copyWith(
          backgroundColor: MaterialStateProperty.resolveWith((states) {
            if (states.contains(MaterialState.pressed)) {
              return isDark
                  ? AppColors.successDark.withOpacity(0.8)
                  : AppColors.success.withOpacity(0.8);
            }
            if (!isEnabled) {
              return successColor.withOpacity(0.5);
            }
            return successColor;
          }),
        );

      case LekkyButtonType.info:
        final infoColor = isDark ? AppColors.infoDark : AppColors.info;
        return ElevatedButton.styleFrom(
          backgroundColor: isEnabled ? infoColor : infoColor.withOpacity(0.5),
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          elevation: isEnabled ? 2 : 0,
        ).copyWith(
          backgroundColor: MaterialStateProperty.resolveWith((states) {
            if (states.contains(MaterialState.pressed)) {
              return isDark
                  ? AppColors.infoDark.withOpacity(0.8)
                  : AppColors.info.withOpacity(0.8);
            }
            if (!isEnabled) {
              return infoColor.withOpacity(0.5);
            }
            return infoColor;
          }),
        );
    }
  }

  /// Get button child widget (text or loading indicator)
  Widget _getButtonChild() {
    if (isLoading) {
      return const SizedBox(
        width: 20,
        height: 20,
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
        ),
      );
    }

    return Text(
      text,
      style: textStyle ??
          const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
    );
  }

  /// Apply size constraints based on button size
  Widget _applySizeConstraints(
      BuildContext context, ButtonStyle style, Widget child) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 360;

    // Get padding based on size
    EdgeInsets padding;
    double? minWidth;
    double minHeight = 48; // Accessibility minimum

    switch (size) {
      case LekkyButtonSize.standard:
        padding = isSmallScreen
            ? const EdgeInsets.symmetric(horizontal: 12, vertical: 8)
            : const EdgeInsets.symmetric(horizontal: 16, vertical: 12);
        break;

      case LekkyButtonSize.compact:
        padding = const EdgeInsets.symmetric(horizontal: 12, vertical: 8);
        minHeight = 40; // Slightly smaller for dialogs
        break;

      case LekkyButtonSize.fullWidth:
        padding = const EdgeInsets.symmetric(horizontal: 16, vertical: 12);
        minWidth = double.infinity;
        break;
    }

    final updatedStyle = style.copyWith(
      padding: MaterialStateProperty.all(padding),
      minimumSize: MaterialStateProperty.all(
        Size(minWidth ?? 0, minHeight),
      ),
    );

    // Return appropriate button widget based on type
    switch (type) {
      case LekkyButtonType.secondary:
        return OutlinedButton(
          onPressed: onPressed,
          style: updatedStyle,
          child: child,
        );
      default:
        return ElevatedButton(
          onPressed: onPressed,
          style: updatedStyle,
          child: child,
        );
    }
  }
}
