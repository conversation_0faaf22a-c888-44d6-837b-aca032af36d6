// File: lib/core/models/average.dart
import 'package:equatable/equatable.dart';

/// Model class for precomputed averages
class Average extends Equatable {
  /// Unique identifier
  final int? id;
  
  /// Recent average daily usage
  final double? recentAverage;
  
  /// Total average daily usage
  final double? totalAverage;
  
  /// Date and time of last update
  final DateTime lastUpdated;

  /// Constructor
  const Average({
    this.id,
    this.recentAverage,
    this.totalAverage,
    required this.lastUpdated,
  });

  /// Create a copy of this average with the given fields replaced
  Average copyWith({
    int? id,
    double? recentAverage,
    double? totalAverage,
    DateTime? lastUpdated,
  }) {
    return Average(
      id: id ?? this.id,
      recentAverage: recentAverage ?? this.recentAverage,
      totalAverage: totalAverage ?? this.totalAverage,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  /// Convert to a map for database operations
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'recent_average': recentAverage,
      'total_average': totalAverage,
      'last_updated': lastUpdated.toIso8601String(),
    };
  }

  /// Create an average from a map
  factory Average.fromMap(Map<String, dynamic> map) {
    return Average(
      id: map['id'] as int?,
      recentAverage: map['recent_average'] as double?,
      totalAverage: map['total_average'] as double?,
      lastUpdated: DateTime.parse(map['last_updated'] as String),
    );
  }

  @override
  List<Object?> get props => [id, recentAverage, totalAverage, lastUpdated];

  @override
  String toString() {
    return 'Average(id: $id, recentAverage: $recentAverage, totalAverage: $totalAverage, lastUpdated: $lastUpdated)';
  }
}
