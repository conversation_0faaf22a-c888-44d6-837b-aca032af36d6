// File: lib/features/cost/domain/models/cost_mode.dart

/// Enum representing cost calculation modes
enum CostMode {
  /// Past mode - calculate costs for past periods
  past('Past'),
  
  /// Future mode - project costs for future periods
  future('Future');

  /// Constructor
  const CostMode(this.displayName);

  /// Display name of the mode
  final String displayName;

  /// Toggle between past and future modes
  CostMode toggle() {
    switch (this) {
      case past:
        return future;
      case future:
        return past;
    }
  }
}
