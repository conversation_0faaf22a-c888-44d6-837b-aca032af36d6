// File: lib/core/utils/performance_monitor.dart
import 'dart:async';
import 'logger.dart';

/// Performance monitoring service for tracking operation timing
class PerformanceMonitor {
  static final Map<String, DateTime> _startTimes = {};
  static final Map<String, List<Duration>> _operationHistory = {};

  /// Start timing an operation
  static void startOperation(String operationName) {
    _startTimes[operationName] = DateTime.now();
    Logger.info('PerformanceMonitor: Started timing $operationName');
  }

  /// End timing an operation and log the duration
  static Duration endOperation(String operationName) {
    final startTime = _startTimes[operationName];
    if (startTime == null) {
      Logger.warning('PerformanceMonitor: No start time found for $operationName');
      return Duration.zero;
    }

    final endTime = DateTime.now();
    final duration = endTime.difference(startTime);
    
    // Store in history
    _operationHistory.putIfAbsent(operationName, () => []).add(duration);
    
    // Keep only last 10 operations for each type
    final history = _operationHistory[operationName]!;
    if (history.length > 10) {
      history.removeAt(0);
    }

    Logger.info('PerformanceMonitor: $operationName completed in ${duration.inMilliseconds}ms');
    
    // Clean up
    _startTimes.remove(operationName);
    
    return duration;
  }

  /// Get average duration for an operation type
  static Duration getAverageDuration(String operationName) {
    final history = _operationHistory[operationName];
    if (history == null || history.isEmpty) {
      return Duration.zero;
    }

    final totalMs = history.fold<int>(0, (sum, duration) => sum + duration.inMilliseconds);
    final averageMs = totalMs ~/ history.length;
    
    return Duration(milliseconds: averageMs);
  }

  /// Get performance summary for an operation type
  static Map<String, dynamic> getOperationSummary(String operationName) {
    final history = _operationHistory[operationName];
    if (history == null || history.isEmpty) {
      return {
        'operationName': operationName,
        'count': 0,
        'averageDuration': 0,
        'lastDuration': 0,
      };
    }

    final averageDuration = getAverageDuration(operationName);
    final lastDuration = history.last;

    return {
      'operationName': operationName,
      'count': history.length,
      'averageDuration': averageDuration.inMilliseconds,
      'lastDuration': lastDuration.inMilliseconds,
    };
  }

  /// Get all performance summaries
  static Map<String, Map<String, dynamic>> getAllSummaries() {
    final summaries = <String, Map<String, dynamic>>{};
    for (final operationName in _operationHistory.keys) {
      summaries[operationName] = getOperationSummary(operationName);
    }
    return summaries;
  }

  /// Clear all performance data
  static void clear() {
    _startTimes.clear();
    _operationHistory.clear();
    Logger.info('PerformanceMonitor: Cleared all performance data');
  }

  /// Time an async operation
  static Future<T> timeOperation<T>(
    String operationName,
    Future<T> Function() operation,
  ) async {
    startOperation(operationName);
    try {
      final result = await operation();
      endOperation(operationName);
      return result;
    } catch (e) {
      endOperation(operationName);
      rethrow;
    }
  }
}
