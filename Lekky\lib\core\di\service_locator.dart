import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:get_it/get_it.dart';
import '../database/database_helper.dart';
import '../services/preference_service.dart';

import '../utils/logger.dart';
import '../../features/meter_readings/domain/repositories/meter_reading_repository.dart';
import '../../features/meter_readings/data/repositories/meter_reading_repository_impl.dart';
import '../../features/top_ups/domain/repositories/top_up_repository.dart';
import '../../features/top_ups/data/repositories/top_up_repository_impl.dart';
import '../../features/home/<USER>/controllers/home_controller.dart';
// Removed: history_controller.dart - migrated to Riverpod
import '../../features/entries/presentation/controllers/entry_controller.dart';
import '../../features/notifications/domain/repositories/notification_repository.dart';
import '../../features/notifications/data/notification_repository_impl.dart';
import '../../features/notifications/data/notification_service.dart';
// Removed: notification_controller.dart - migrated to Riverpod
import '../../features/validation/domain/services/data_integrity_service.dart';
import '../../features/validation/domain/services/validation_trigger_service.dart';
import '../../features/validation/domain/services/meter_reading_validator.dart';
import '../../features/validation/presentation/controllers/validation_dashboard_controller.dart';
import '../../features/cost/data/cost_repository.dart';
import '../../features/cost/domain/services/hybrid_cost_service.dart';

import '../../features/averages/domain/repositories/average_repository.dart';
import '../../features/averages/data/repositories/average_repository_impl.dart';
import '../../features/averages/domain/repositories/per_reading_average_repository.dart';
import '../../features/averages/data/repositories/per_reading_average_repository_impl.dart';
import '../../features/averages/domain/services/average_service.dart';

/// Global service locator instance
final GetIt serviceLocator = GetIt.instance;

/// Initialize the service locator with all dependencies
void setupServiceLocator() {
  // Core services
  serviceLocator.registerLazySingleton<DatabaseHelper>(() => DatabaseHelper());
  serviceLocator
      .registerLazySingleton<PreferenceService>(() => PreferenceService());

  // Flutter Local Notifications Plugin
  serviceLocator.registerLazySingleton<FlutterLocalNotificationsPlugin>(
    () => FlutterLocalNotificationsPlugin(),
  );

  // Repositories
  serviceLocator.registerLazySingleton<MeterReadingRepository>(
    () => MeterReadingRepositoryImpl(serviceLocator<DatabaseHelper>()),
  );

  serviceLocator.registerLazySingleton<TopUpRepository>(
    () => TopUpRepositoryImpl(serviceLocator<DatabaseHelper>()),
  );

  // Register NotificationRepository as a lazy singleton to ensure consistent instance
  serviceLocator.registerLazySingletonAsync<NotificationRepository>(
    () async {
      try {
        final db = await serviceLocator<DatabaseHelper>().database;
        final repo = NotificationRepositoryImpl(db);

        // Verify the notifications table exists
        try {
          // Check if table exists
          final tables = await db.rawQuery(
              "SELECT name FROM sqlite_master WHERE type='table' AND name='notifications'");

          if (tables.isEmpty) {
            Logger.error('Notifications table does not exist, creating it now');
            await NotificationRepositoryImpl.createTable(db);
          }
        } catch (e) {
          Logger.error('Failed to verify notifications table: $e');
          // Create the table anyway as a fallback
          await NotificationRepositoryImpl.createTable(db);
        }

        return repo;
      } catch (e) {
        Logger.error('Failed to initialize NotificationRepository: $e');
        rethrow;
      }
    },
  );

  // Controllers
  serviceLocator.registerFactory<HomeController>(
    () => HomeController(
      meterReadingRepository: serviceLocator<MeterReadingRepository>(),
      topUpRepository: serviceLocator<TopUpRepository>(),
    ),
  );

  // HistoryController removed - migrated to Riverpod in Phase 2

  serviceLocator.registerFactory<EntryController>(
    () => EntryController(
      meterReadingRepository: serviceLocator<MeterReadingRepository>(),
      topUpRepository: serviceLocator<TopUpRepository>(),
    ),
  );

  // Register NotificationService as a lazy singleton to ensure consistent instance
  serviceLocator.registerLazySingletonAsync<NotificationService>(
    () async {
      try {
        return NotificationService(
          serviceLocator<FlutterLocalNotificationsPlugin>(),
          await serviceLocator.getAsync<NotificationRepository>(),
        );
      } catch (e) {
        Logger.error('Failed to initialize NotificationService: $e');
        rethrow;
      }
    },
  );

  // NotificationController removed - migrated to Riverpod in Phase 3

  // Register MeterReadingValidator
  serviceLocator.registerLazySingleton<MeterReadingValidator>(
    () => MeterReadingValidator(
      meterReadingRepository: serviceLocator<MeterReadingRepository>(),
      topUpRepository: serviceLocator<TopUpRepository>(),
    ),
  );

  // Register DataIntegrityService
  serviceLocator.registerLazySingleton<DataIntegrityService>(
    () => DataIntegrityService(
      meterReadingRepository: serviceLocator<MeterReadingRepository>(),
      topUpRepository: serviceLocator<TopUpRepository>(),
    ),
  );

  // Register ValidationTriggerService
  serviceLocator.registerLazySingleton<ValidationTriggerService>(
    () => ValidationTriggerService(
      dataIntegrityService: serviceLocator<DataIntegrityService>(),
      meterReadingRepository: serviceLocator<MeterReadingRepository>(),
      topUpRepository: serviceLocator<TopUpRepository>(),
      validator: serviceLocator<MeterReadingValidator>(),
    ),
  );

  // Register ValidationDashboardController
  serviceLocator.registerFactory<ValidationDashboardController>(
    () => ValidationDashboardController(
      dataIntegrityService: serviceLocator<DataIntegrityService>(),
      meterReadingRepository: serviceLocator<MeterReadingRepository>(),
      topUpRepository: serviceLocator<TopUpRepository>(),
    ),
  );

  // Register cost services
  serviceLocator.registerLazySingleton<HybridCostService>(
    () => HybridCostService(
      serviceLocator<MeterReadingRepository>(),
      serviceLocator<TopUpRepository>(),
      serviceLocator<PerReadingAverageRepository>(),
    ),
  );

  serviceLocator.registerLazySingleton<CostRepository>(
    () => CostRepository(),
  );

  // CostController removed - migrated to Riverpod in Phase 5

  // Register average services
  serviceLocator.registerLazySingleton<AverageRepository>(
    () => AverageRepositoryImpl(serviceLocator<DatabaseHelper>()),
  );

  serviceLocator.registerLazySingleton<PerReadingAverageRepository>(
    () => PerReadingAverageRepositoryImpl(serviceLocator<DatabaseHelper>()),
  );

  serviceLocator.registerLazySingleton<AverageService>(
    () => AverageService(),
  );
}
