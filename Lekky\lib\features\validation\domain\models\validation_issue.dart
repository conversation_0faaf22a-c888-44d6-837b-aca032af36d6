// File: lib/features/validation/domain/models/validation_issue.dart

/// Types of validation issues that can be detected
enum ValidationIssueType {
  /// Value is negative when it should be positive
  negativeValue,
  
  /// Date is in the future
  futureDate,
  
  /// Entries are not in chronological order
  chronologicalOrder,
  
  /// Balance inconsistency between readings and top-ups
  balanceInconsistency,
  
  /// Duplicate entry detected
  duplicateEntry,
  
  /// Missing entry detected (gap in dates)
  missingEntry,
  
  /// Other unspecified issue
  other,
}

/// Severity levels for validation issues
enum ValidationIssueSeverity {
  /// Low severity - informational only
  low,
  
  /// Medium severity - should be fixed but not critical
  medium,
  
  /// High severity - critical issue that should be fixed immediately
  high,
}

/// Model class for validation issues
class ValidationIssue {
  /// ID of the entry with the issue (can be null for global issues)
  final int? entryId;
  
  /// Type of validation issue
  final ValidationIssueType type;
  
  /// Severity of the issue
  final ValidationIssueSeverity severity;
  
  /// Human-readable message describing the issue
  final String message;
  
  /// When the issue was detected
  final DateTime detectedAt;
  
  /// Additional data related to the issue
  final Map<String, dynamic>? metadata;

  /// Constructor
  ValidationIssue({
    this.entryId,
    required this.type,
    required this.severity,
    required this.message,
    DateTime? detectedAt,
    this.metadata,
  }) : detectedAt = detectedAt ?? DateTime.now();
  
  /// Create a copy of this issue with optional new values
  ValidationIssue copyWith({
    int? entryId,
    ValidationIssueType? type,
    ValidationIssueSeverity? severity,
    String? message,
    DateTime? detectedAt,
    Map<String, dynamic>? metadata,
  }) {
    return ValidationIssue(
      entryId: entryId ?? this.entryId,
      type: type ?? this.type,
      severity: severity ?? this.severity,
      message: message ?? this.message,
      detectedAt: detectedAt ?? this.detectedAt,
      metadata: metadata ?? this.metadata,
    );
  }
  
  /// Convert to a map for database operations
  Map<String, dynamic> toMap() {
    return {
      'entry_id': entryId,
      'type': type.index,
      'severity': severity.index,
      'message': message,
      'detected_at': detectedAt.toIso8601String(),
      'metadata': metadata != null ? metadata.toString() : null,
    };
  }
  
  /// Create an issue from a map
  factory ValidationIssue.fromMap(Map<String, dynamic> map) {
    return ValidationIssue(
      entryId: map['entry_id'] as int?,
      type: ValidationIssueType.values[map['type'] as int],
      severity: ValidationIssueSeverity.values[map['severity'] as int],
      message: map['message'] as String,
      detectedAt: DateTime.parse(map['detected_at'] as String),
      metadata: map['metadata'] != null 
          ? Map<String, dynamic>.from(map['metadata'] as Map<String, dynamic>) 
          : null,
    );
  }
  
  @override
  String toString() {
    return 'ValidationIssue(entryId: $entryId, type: $type, severity: $severity, message: $message)';
  }
}
