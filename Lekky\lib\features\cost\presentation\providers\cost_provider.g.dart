// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'cost_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

// ignore_for_file: avoid_private_typedef_functions, non_constant_identifier_names, subtype_of_sealed_class, invalid_use_of_internal_member, unused_element, constant_identifier_names, unnecessary_raw_strings, library_private_types_in_public_api

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

String _$CostHash() => r'76aacfc5690c06e533818d43fc9b4c4c7358315a';

/// Cost analysis provider with comprehensive cost calculation and chart management
///
/// Copied from [Cost].
final costProvider = AutoDisposeAsyncNotifierProvider<Cost, CostState>(
  Cost.new,
  name: r'costProvider',
);
typedef CostRef = AutoDisposeAsyncNotifierProviderRef<CostState>;

abstract class _$Cost extends AutoDisposeAsyncNotifier<CostState> {
  @override
  FutureOr<CostState> build();
}

String _$costRepositoryHash() => r'9aa089c7a3ab1bf6de438adfc086addcee79a6b0';

/// Cost repository provider
///
/// Copied from [costRepository].
final costRepositoryProvider = AutoDisposeProvider<CostRepository>(
  costRepository,
  name: r'costRepositoryProvider',
);
typedef CostRepositoryRef = AutoDisposeProviderRef<CostRepository>;
