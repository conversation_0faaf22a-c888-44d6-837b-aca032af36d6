// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'riverpod_localization_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$riverpodLocalizationHash() => r'b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0u1';

/// Provider for localization management using Riverpod
///
/// Copied from [RiverpodLocalization].
@ProviderFor(RiverpodLocalization)
final riverpodLocalizationProvider = AutoDisposeAsyncNotifierProvider<RiverpodLocalization, LocalizationState>.internal(
  RiverpodLocalization.new,
  name: r'riverpodLocalizationProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$riverpodLocalizationHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$RiverpodLocalization = AutoDisposeAsyncNotifier<LocalizationState>;
