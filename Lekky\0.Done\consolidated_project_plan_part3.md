# NewLekky Consolidated Project Plan - Part 3: Feature Modules and UI Components

This document provides detailed specifications for the feature modules and UI components of the NewLekky app.

## App Flow & Feature Modules

### App Flow
- **First Launch**: SplashScreen → WelcomePage → SetupPage → HomePage
- **Subsequent Launches**: SplashScreen → HomePage
- Request file permissions on first launch
- Offline-first architecture supporting up to 630 entries
- Dynamic language switching with RTL support

### Core Modules

1. **SplashModule**
   - Handles app initialization
   - Checks permissions
   - Determines first-time use
   - Loads initial state

2. **WelcomeModule**
   - First-time user experience
   - Data restoration option
   - Initial navigation
   - Onboarding flow

3. **SetupModule**
   - Language selection
   - Currency configuration
   - Initial settings setup
   - First meter reading entry
   - Permission requests

4. **DatabaseModule**
   - Database initialization
   - CRUD operations
   - Data validation
   - Migration handling
   - Backup and restore functionality:
     - CSV export to Downloads folder as `lekky_export_[version].csv`
     - Version number included in header
     - Import from CSV with version validation

5. **CurrentStateModule**
   - State management
   - Settings persistence
   - State synchronization
   - Theme management

### Feature Modules

6. **HomeModule**
   - Main dashboard
   - Current meter status
   - Quick actions
   - Message bar
   - Usage statistics
   - Remaining days projection

7. **EntryModule**
   - Add/edit meter readings
   - Add/edit top-ups
   - Entry validation
   - Date/time selection
   - Notes and additional information

8. **CalculationModule**
   - Recent average calculations
   - Total average calculations
   - Validity checks
   - Projection calculations
   - Seasonal adjustments
   - Statistical analysis

9. **HistoryModule**
   - History list view with pagination
   - Advanced filtering and sorting:
     - Apply date filters first, then secondary filters (all/readings/top-ups/invalid)
   - Entry management (add, edit, delete)
   - Data visualization
   - Export functionality
   - Color coding:
     - Dark blue text for meter readings
     - Orange text for top-ups
     - Warning icons (yellow triangles) for invalid entries
   - Table display:
     - Vertical scrolling
     - Table will be sized appropriately to screen size (rows plus header)
     - Ensures appropriate spacing and screen position based on screen percentage

   **Visualization Features**:
   - Line charts showing usage trends over time
   - Interactive charts with tap-to-view details
   - Time period selection (daily, weekly, monthly, yearly)
   - Missing data handling (interpolation or gaps)
   - Export options for visualization data

10. **CostModule**
    - Cost calculations based on usage
    - Cost projections with seasonal adjustments
    - Cost history with breakdown
    - Advanced cost visualization
    - Comparative analysis

    **Visualization Features**:
    - Bar charts for cost breakdowns by period
    - Comparative analysis (current vs. previous periods)
    - Cost trend projections
    - Interactive elements for detailed exploration
    - Customizable time ranges

11. **SettingsModule**
    - Settings categories (only one menu branch open at a time):
      - Region
      - Alerts & Notifications
      - Date Settings
      - Appearance
      - Data Management
      - About
      - Donate
      - Testing & Debug
    - Preference management (changes apply immediately)
    - Data management
    - Theme selection
    - Language selection
    - Notification preferences
    - PayPal donations:
      - Live implementation (no sandbox)
      - 2-column preset amounts
      - Thank you confirmation

12. **NotificationModule**
    - Notification scheduling and management (works when app is closed or phone is locked)
    - Configurable reminder system
    - Smart alert generation
    - Notification display and interaction
    - Notification preferences management
    - Implementation via flutter_local_notifications + permission_handler (Android 13+)

    **Notification Types**:
    - Low Balance: When projected remaining days fall below threshold
      - Considers days since last reading, Alert Threshold, Days In Advance settings
      - Uses recent average usage for calculations
    - Time to Top-Up: When balance projected to run out within set period
    - Invalid Records: When validation of entries fails
    - Reading Reminders: Scheduled prompts to take meter readings
      - When frequency is not daily, shows calendar for first date selection, then time picker

    **Customization Options**:
    - Enable/disable specific notification types
    - Set notification sound and vibration preferences
    - Configure notification channels (silent, urgent)
    - Schedule frequency (daily, weekly, bi-weekly, monthly)

## UI Components and Screens

### Main Screens

1. **Splash Screen**
   - App logo
   - Loading indicator
   - Version information
   - Permission requests
   - Initialization progress

2. **Welcome Screen**
   - Welcome message
   - Restore data option
   - Continue button
   - Brief app introduction

3. **Setup Screen**
   - Multi-step setup process
   - Language selection
   - Currency selection
   - Initial settings configuration
   - First meter reading entry

4. **Home Screen**
   - Current meter reading
   - Usage statistics
   - Quick actions
   - Navigation bar
   - Message bar
   - Remaining days indicator
   - Recent activity summary

5. **Cost Screen**
   - Cost calculations
   - Cost projections
   - Cost history
   - Cost breakdown
   - Time period selection
   - Visualization options

6. **History Screen**
   - Filterable entry list
   - Color-coded entries
   - Warning indicators
   - Pagination controls
   - Sorting options
   - Search functionality
   - Edit mode toggle

7. **Settings Screen**
   - Categorized settings
   - Toggle options
   - Radio selections
   - Data management options
   - About section
   - Help and support

### Dialogs and Modals

1. **Add Entry Dialog**
   - Date/time picker
   - Value input
   - Type selection (reading/top-up)
   - Validation feedback
   - Notes field
   - Save/cancel buttons

2. **Edit Entry Dialog**
   - Pre-filled entry data
   - Modification options
   - Delete option
   - Validation feedback
   - History context

3. **Notification Dialog**
   - Notification details
   - Action buttons
   - Dismissal option
   - Snooze option

4. **Confirmation Dialog**
   - Action confirmation
   - Cancel option
   - Warning information
   - Don't show again option

5. **Filter Dialog**
   - Date range selection
   - Entry type filters
   - Validity filters
   - Sort options
   - Apply/reset buttons

### Reusable Components

1. **Message Bar**
   - Notification display
   - Alert messages
   - Status updates
   - Action buttons
   - Dismissal option

2. **Navigation Bar**
   - Fixed position
   - Screen navigation
   - Active indicators
   - Badge notifications

3. **Entry Card**
   - Entry details
   - Color coding
   - Warning indicators
   - Action buttons
   - Expandable details

4. **Settings Toggle**
   - Label
   - Toggle switch
   - Description
   - Conditional options

5. **Chart Component**
   - Data visualization
   - Interactive elements
   - Time period selection
   - Legend
   - Export options

6. **Validation Indicator**
   - Warning icon
   - Error message
   - Suggestion
   - Action button

7. **Empty State**
   - Illustration
   - Message
   - Action button
   - Help text

8. **Loading Indicator**
   - Progress animation
   - Status message
   - Cancel option
   - Background dimming

## UI/UX Guidelines

### Navigation and Layout
- Fixed bottom navigation bar that remains visible with keyboard
- History table should have vertical scrolling, sized appropriately to screen dimensions
- All text should be light and dark mode sensitive

### Button Styling
- Consistent button styling:
  - Cancel buttons: Blue outline
  - Delete buttons: Solid red
  - Edit Now buttons: Dark orange

### Color Palette

#### Light Mode
- **Primary**: #0288D1 (Blue 600)
- **Secondary**: #FFA000 (Amber 700)
- **Tertiary**: #FFCA28 (Amber 300)
- **Error**: #D32F2F (Red 700)
- **Background**: #F8F9FA (Very light gray)
- **Surface**: #FFFFFF (White)
- **On Primary**: #FFFFFF (White)
- **On Secondary**: #000000 (Black)
- **On Background**: #212121 (Dark gray)

#### Dark Mode
- **Primary**: #42A5F5 (Blue 400)
- **Secondary**: #90CAF9 (Blue 200)
- **Tertiary**: #FFB74D (Amber 300)
- **Error**: #E57373 (Red 300)
- **Background**: #121212 (Dark gray)
- **Surface**: #1E1E1E (Slightly lighter gray)
- **On Primary**: #000000 (Black)
- **On Secondary**: #000000 (Black)
- **On Background**: #E1E1E1 (Light gray)

### Typography

- **Headings**: Roboto, bold, sizes 24/20/18/16
- **Body**: Roboto, regular, sizes 16/14
- **Caption**: Roboto, light, size 12
- **Button**: Roboto, medium, size 14
- **Input**: Roboto, regular, size 16

### Spacing

- **Base unit**: 8dp
- **Content padding**: 16dp
- **Item spacing**: 8dp
- **Section spacing**: 24dp
- **Screen padding**: 16dp

### Accessibility

- Minimum touch target size: 48x48dp
- Color contrast ratio: 4.5:1 minimum
- Text scaling support up to 200%
- Screen reader compatibility
- Keyboard navigation support
