import 'package:shared_preferences/shared_preferences.dart';
import '../../../../core/shared/models/date_format.dart';
import '../../../../core/shared/models/theme_mode.dart';
import '../../../../core/constants/preference_keys.dart';

/// Model class for setup preferences
class SetupPreferences {
  /// Date format setting
  final DateFormat dateFormat;

  /// Whether to show time with date
  final bool showTimeWithDate;

  /// Alert threshold value
  final double alertThreshold;

  /// Days in advance for alerts
  final int daysInAdvance;

  /// Initial meter reading
  final double? initialMeterReading;

  /// Selected language
  final String language;

  /// Selected currency
  final String currency;

  /// Currency symbol
  final String currencySymbol;

  /// Theme mode
  final AppThemeMode themeMode;

  /// Constructor with default values
  SetupPreferences({
    this.dateFormat = DateFormat.ddMmYyyy,
    this.showTimeWithDate = true,
    this.alertThreshold = 5.0,
    this.daysInAdvance = 5,
    this.initialMeterReading,
    this.language = 'English',
    this.currency = 'GBP',
    this.currencySymbol = '£',
    this.themeMode = AppThemeMode.system,
  });

  /// Create a copy with some fields replaced
  SetupPreferences copyWith({
    DateFormat? dateFormat,
    bool? showTimeWithDate,
    double? alertThreshold,
    int? daysInAdvance,
    double? initialMeterReading,
    String? language,
    String? currency,
    String? currencySymbol,
    AppThemeMode? themeMode,
  }) {
    return SetupPreferences(
      dateFormat: dateFormat ?? this.dateFormat,
      showTimeWithDate: showTimeWithDate ?? this.showTimeWithDate,
      alertThreshold: alertThreshold ?? this.alertThreshold,
      daysInAdvance: daysInAdvance ?? this.daysInAdvance,
      initialMeterReading: initialMeterReading ?? this.initialMeterReading,
      language: language ?? this.language,
      currency: currency ?? this.currency,
      currencySymbol: currencySymbol ?? this.currencySymbol,
      themeMode: themeMode ?? this.themeMode,
    );
  }

  /// Save preferences to SharedPreferences
  Future<void> saveToPreferences() async {
    final prefs = await SharedPreferences.getInstance();

    await prefs.setString(PreferenceKeys.dateFormat, dateFormat.formatString);
    await prefs.setBool(PreferenceKeys.showTimeWithDate, showTimeWithDate);
    await prefs.setDouble(PreferenceKeys.alertThreshold, alertThreshold);
    await prefs.setInt(PreferenceKeys.daysInAdvance, daysInAdvance);

    if (initialMeterReading != null) {
      await prefs.setDouble(
          PreferenceKeys.initialMeterReading, initialMeterReading!);
    }

    await prefs.setString(PreferenceKeys.language, language);
    await prefs.setString(PreferenceKeys.currency, currency);
    await prefs.setString(PreferenceKeys.currencySymbol, currencySymbol);
    await prefs.setString(PreferenceKeys.themeMode, themeMode.toString());

    // Mark setup as completed
    await prefs.setBool(PreferenceKeys.setupCompleted, true);
  }

  /// Load preferences from SharedPreferences
  static Future<SetupPreferences> loadFromPreferences() async {
    final prefs = await SharedPreferences.getInstance();

    final dateFormatStr =
        prefs.getString(PreferenceKeys.dateFormat) ?? 'dd-MM-yyyy';
    final showTimeWithDate =
        prefs.getBool(PreferenceKeys.showTimeWithDate) ?? true;
    final alertThreshold =
        prefs.getDouble(PreferenceKeys.alertThreshold) ?? 5.0;
    final daysInAdvance = prefs.getInt(PreferenceKeys.daysInAdvance) ?? 5;
    final initialMeterReading =
        prefs.getDouble(PreferenceKeys.initialMeterReading);
    final language = prefs.getString(PreferenceKeys.language) ?? 'English';
    final currency = prefs.getString(PreferenceKeys.currency) ?? 'GBP';
    final currencySymbol =
        prefs.getString(PreferenceKeys.currencySymbol) ?? '£';
    final themeModeStr = prefs.getString(PreferenceKeys.themeMode);
    final themeMode = themeModeStr != null
        ? AppThemeMode.fromString(themeModeStr)
        : AppThemeMode.system;

    return SetupPreferences(
      dateFormat: DateFormat.fromString(dateFormatStr),
      showTimeWithDate: showTimeWithDate,
      alertThreshold: alertThreshold,
      daysInAdvance: daysInAdvance,
      initialMeterReading: initialMeterReading,
      language: language,
      currency: currency,
      currencySymbol: currencySymbol,
      themeMode: themeMode,
    );
  }

  /// Check if setup has been completed
  static Future<bool> isSetupCompleted() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(PreferenceKeys.setupCompleted) ?? false;
  }

  /// For debugging
  @override
  String toString() {
    return 'SetupPreferences(dateFormat: $dateFormat, showTimeWithDate: $showTimeWithDate, '
        'alertThreshold: $alertThreshold, daysInAdvance: $daysInAdvance, '
        'initialMeterReading: $initialMeterReading, language: $language, '
        'currency: $currency, currencySymbol: $currencySymbol, themeMode: $themeMode)';
  }

  /// Equality operator
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is SetupPreferences &&
        other.dateFormat == dateFormat &&
        other.showTimeWithDate == showTimeWithDate &&
        other.alertThreshold == alertThreshold &&
        other.daysInAdvance == daysInAdvance &&
        other.initialMeterReading == initialMeterReading &&
        other.language == language &&
        other.currency == currency &&
        other.currencySymbol == currencySymbol &&
        other.themeMode == themeMode;
  }

  /// Hash code
  @override
  int get hashCode {
    return dateFormat.hashCode ^
        showTimeWithDate.hashCode ^
        alertThreshold.hashCode ^
        daysInAdvance.hashCode ^
        initialMeterReading.hashCode ^
        language.hashCode ^
        currency.hashCode ^
        currencySymbol.hashCode ^
        themeMode.hashCode;
  }
}
