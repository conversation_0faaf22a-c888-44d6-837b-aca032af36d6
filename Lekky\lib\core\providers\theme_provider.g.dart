// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'theme_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$themeHash() => r'a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0';

/// Provider for theme management using Riverpod
///
/// Copied from [Theme].
@ProviderFor(Theme)
final themeProvider = AutoDisposeAsyncNotifierProvider<Theme, ThemeState>.internal(
  Theme.new,
  name: r'themeProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$themeHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$Theme = AutoDisposeAsyncNotifier<ThemeState>;
