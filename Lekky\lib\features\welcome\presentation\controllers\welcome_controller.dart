import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Controller for the welcome screen
class WelcomeController extends ChangeNotifier {
  /// Whether the welcome screen has been completed
  bool _welcomeCompleted = false;

  /// Whether the controller is loading
  bool _isLoading = false;

  /// Error message if any
  String? _error;

  /// Get whether the welcome screen has been completed
  bool get welcomeCompleted => _welcomeCompleted;

  /// Get whether the controller is loading
  bool get isLoading => _isLoading;

  /// Get the error message if any
  String? get error => _error;

  /// Constructor
  WelcomeController() {
    _checkWelcomeStatus();
  }

  /// Check if the welcome screen has been completed
  Future<void> _checkWelcomeStatus() async {
    try {
      _isLoading = true;
      notifyListeners();

      final prefs = await SharedPreferences.getInstance();
      _welcomeCompleted = prefs.getBool('welcome_completed') ?? false;

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
    }
  }

  /// Mark the welcome screen as completed
  Future<void> markWelcomeCompleted() async {
    try {
      _isLoading = true;
      notifyListeners();

      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('welcome_completed', true);
      _welcomeCompleted = true;

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
    }
  }
}
