// File: lib/features/cost/presentation/widgets/period_selector.dart
import 'package:flutter/material.dart';
import '../../domain/models/cost_period.dart';
import '../../../../core/widgets/app_card.dart';

/// Widget for selecting cost calculation period
class PeriodSelector extends StatelessWidget {
  final CostPeriod selectedPeriod;
  final ValueChanged<CostPeriod> onPeriodChanged;

  const PeriodSelector({
    super.key,
    required this.selectedPeriod,
    required this.onPeriodChanged,
  });

  @override
  Widget build(BuildContext context) {
    return AppCard(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Period',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: CostPeriod.values.map((period) {
                final isSelected = period == selectedPeriod;
                return FilterChip(
                  label: Text(_getPeriodLabel(period)),
                  selected: isSelected,
                  onSelected: (_) => onPeriodChanged(period),
                  selectedColor:
                      Theme.of(context).colorScheme.primary.withOpacity(0.2),
                  checkmarkColor: Theme.of(context).colorScheme.primary,
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  String _getPeriodLabel(CostPeriod period) {
    switch (period) {
      case CostPeriod.pastDay:
        return 'Past Day';
      case CostPeriod.pastWeek:
        return 'Past Week';
      case CostPeriod.pastMonth:
        return 'Past Month';
      case CostPeriod.pastYear:
        return 'Past Year';
      case CostPeriod.futureDay:
        return 'Future Day';
      case CostPeriod.futureWeek:
        return 'Future Week';
      case CostPeriod.futureMonth:
        return 'Future Month';
      case CostPeriod.futureYear:
        return 'Future Year';
      case CostPeriod.custom:
        return 'Custom';
    }
  }
}
