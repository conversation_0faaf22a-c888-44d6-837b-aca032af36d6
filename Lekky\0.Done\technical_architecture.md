# Technical Architecture for NewLekky App

This document outlines the technical architecture for the NewLekky app, providing detailed information on the implementation approach, patterns, and technologies.

## Architecture Overview

The NewLekky app follows a feature-first modular architecture with clear separation of concerns. The architecture is designed to be:

- **Maintainable**: Clear separation of concerns and modular design
- **Testable**: Business logic isolated from UI for easy testing
- **Scalable**: Ability to add new features without affecting existing ones
- **Efficient**: Optimized for performance with large datasets

### Architectural Layers

The app is divided into three main layers:

1. **Presentation Layer**: UI components, screens, and widgets
2. **Domain Layer**: Business logic, use cases, and entities
3. **Data Layer**: Repositories, data sources, and models

## Directory Structure

```
lib/
├── core/                  # Core functionality used across the app
│   ├── constants/         # App-wide constants
│   ├── error/             # Error handling
│   ├── navigation/        # Navigation service and routes
│   ├── theme/             # Theme configuration
│   ├── utils/             # Utility functions
│   └── widgets/           # Shared widgets
│
├── data/                  # Data layer
│   ├── datasources/       # Local data sources
│   ├── models/            # Data models
│   └── repositories/      # Repository implementations
│
├── domain/                # Domain layer
│   ├── entities/          # Business entities
│   ├── repositories/      # Repository interfaces
│   └── usecases/          # Business logic use cases
│
├── features/              # Feature modules
│   ├── home/              # Home feature
│   │   ├── presentation/  # UI components
│   │   ├── domain/        # Feature-specific business logic
│   │   └── data/          # Feature-specific data handling
│   ├── history/           # History feature
│   ├── cost/              # Cost feature
│   ├── settings/          # Settings feature
│   └── ...                # Other features
│
├── di/                    # Dependency injection
│   └── injection.dart     # Dependency registration
│
└── main.dart              # App entry point
```

## State Management

### Riverpod Implementation

The app uses Riverpod for state management, providing a clean, testable approach with strong typing:

```dart
// Global providers
final currentStateProvider = StateNotifierProvider<CurrentStateNotifier, CurrentState>((ref) {
  return CurrentStateNotifier(ref.read(settingsRepositoryProvider));
});

// Feature-specific providers
final meterReadingsProvider = StateNotifierProvider<MeterReadingsNotifier, AsyncValue<List<MeterReading>>>((ref) {
  return MeterReadingsNotifier(ref.read(meterReadingRepositoryProvider));
});

// Computed/derived state
final daysRemainingProvider = Provider<AsyncValue<double>>((ref) {
  final meterReadings = ref.watch(meterReadingsProvider);
  final calculations = ref.watch(calculationsProvider);
  
  return meterReadings.whenData((readings) {
    return calculations.calculateDaysRemaining(readings);
  });
});
```

### State Organization

1. **Global State**: App-wide settings, user preferences, and current status
2. **Feature State**: State specific to individual features
3. **UI State**: Temporary state for UI components (form values, scroll positions)
4. **Derived State**: Calculated values based on other state

## Database Design

### SQLite Implementation

The app uses SQLite with sqflite for local data storage:

```dart
class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  static Database? _database;

  factory DatabaseHelper() => _instance;

  DatabaseHelper._internal();

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    // Database initialization logic
  }
}
```

### Migration Strategy

```dart
Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
  // Create backup before migration
  await _createBackup();
  
  // Run migrations sequentially
  if (oldVersion < 2) {
    await _migrateV1ToV2(db);
  }
  if (oldVersion < 3) {
    await _migrateV2ToV3(db);
  }
  // Additional migrations...
}

Future<void> _migrateV1ToV2(Database db) async {
  // Migration logic
}
```

## Navigation

### go_router Implementation

```dart
final router = GoRouter(
  initialLocation: '/splash',
  routes: [
    GoRoute(
      path: '/splash',
      builder: (context, state) => const SplashScreen(),
    ),
    GoRoute(
      path: '/welcome',
      builder: (context, state) => const WelcomeScreen(),
    ),
    ShellRoute(
      builder: (context, state, child) => MainLayout(child: child),
      routes: [
        GoRoute(
          path: '/home',
          builder: (context, state) => const HomeScreen(),
        ),
        GoRoute(
          path: '/history',
          builder: (context, state) => const HistoryScreen(),
        ),
        GoRoute(
          path: '/cost',
          builder: (context, state) => const CostScreen(),
        ),
      ],
    ),
    // Additional routes...
  ],
);
```

### Navigation Service

```dart
class NavigationService {
  final GoRouter router;
  
  NavigationService(this.router);
  
  void navigateTo(String route, {Map<String, String>? params}) {
    // Navigation logic
  }
  
  void goBack() {
    // Back navigation logic
  }
}
```

## Dependency Injection

### get_it Implementation

```dart
final getIt = GetIt.instance;

@InjectableInit()
void configureDependencies() => getIt.init();

@module
abstract class AppModule {
  @singleton
  DatabaseHelper get databaseHelper => DatabaseHelper();
  
  @singleton
  NavigationService get navigationService => NavigationService(getIt<GoRouter>());
  
  @singleton
  MeterReadingRepository get meterReadingRepository => 
      MeterReadingRepositoryImpl(getIt<DatabaseHelper>());
}
```

## Error Handling

### Error Types

```dart
abstract class AppError extends Error {
  final String message;
  final StackTrace? stackTrace;
  
  AppError(this.message, [this.stackTrace]);
}

class DatabaseError extends AppError {
  final String operation;
  
  DatabaseError(this.operation, String message, [StackTrace? stackTrace]) 
      : super('Database error during $operation: $message', stackTrace);
}

class ValidationError extends AppError {
  final String field;
  
  ValidationError(this.field, String message, [StackTrace? stackTrace])
      : super('Validation error for $field: $message', stackTrace);
}
```

### Error Handling Strategy

```dart
Result<T> tryCatch<T>(T Function() function) {
  try {
    return Success(function());
  } on DatabaseError catch (e) {
    logError(e);
    return Failure(e);
  } on ValidationError catch (e) {
    logError(e);
    return Failure(e);
  } catch (e, stackTrace) {
    final error = AppError('Unexpected error: $e', stackTrace);
    logError(error);
    return Failure(error);
  }
}
```

## Data Validation

### Validation Rules

```dart
class MeterReadingValidator {
  ValidationResult validate(MeterReading reading, List<MeterReading> previousReadings) {
    final errors = <ValidationError>[];
    final warnings = <ValidationWarning>[];
    
    // Required rules
    if (reading.value < 0) {
      errors.add(ValidationError('value', 'Meter reading must be a positive number'));
    }
    
    // Check chronological order
    if (previousReadings.isNotEmpty && 
        reading.date.isBefore(previousReadings.first.date)) {
      errors.add(ValidationError('date', 'Date must be after previous reading date'));
    }
    
    // Warning rules
    if (previousReadings.isNotEmpty) {
      final lastReading = previousReadings.first;
      final averageUsage = calculateAverageUsage(previousReadings);
      
      if (reading.value > lastReading.value * 2) {
        warnings.add(ValidationWarning('value', 'This reading is unusually high'));
      }
    }
    
    return ValidationResult(errors: errors, warnings: warnings);
  }
}
```

## UI Components

### Reusable Components

```dart
class AppButton extends StatelessWidget {
  final String text;
  final VoidCallback onPressed;
  final ButtonType type;
  
  const AppButton({
    Key? key,
    required this.text,
    required this.onPressed,
    this.type = ButtonType.primary,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    // Button implementation
  }
}

class EntryCard extends StatelessWidget {
  final Entry entry;
  final VoidCallback onTap;
  final bool isValid;
  
  const EntryCard({
    Key? key,
    required this.entry,
    required this.onTap,
    this.isValid = true,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    // Card implementation
  }
}
```

## Testing Strategy

### Unit Testing

```dart
void main() {
  group('MeterReadingValidator', () {
    late MeterReadingValidator validator;
    
    setUp(() {
      validator = MeterReadingValidator();
    });
    
    test('should return error when value is negative', () {
      // Test implementation
    });
    
    test('should return error when date is before previous reading', () {
      // Test implementation
    });
  });
}
```

### Widget Testing

```dart
void main() {
  group('EntryCard', () {
    testWidgets('should display entry details', (WidgetTester tester) async {
      // Test implementation
    });
    
    testWidgets('should call onTap when tapped', (WidgetTester tester) async {
      // Test implementation
    });
  });
}
```

## Performance Optimization

### Database Optimization

- Indexed queries for frequently accessed fields
- Batch operations for multiple updates
- Lazy loading with pagination
- Connection pooling and transaction batching

### UI Performance

- Use `const` constructors where possible
- Implement `RepaintBoundary` for complex widgets
- Use `ListView.builder` for efficient list rendering
- Memoize expensive calculations

## Security Considerations

- Encrypt sensitive data using secure storage
- Implement proper permission handling
- Validate all user input
- Sanitize data before display

## Conclusion

This technical architecture provides a solid foundation for implementing the NewLekky app. By following these patterns and approaches, the development team can create a robust, efficient, and maintainable application that meets the requirements and provides a great user experience.
