# Validation Rules for NewLekky App

This document defines the validation rules for the NewLekky app, ensuring data integrity and providing a consistent user experience.

## Meter Reading Validation

### Required Rules (Must Pass)

| Rule | Description | Error Message | Recovery Action |
|------|-------------|---------------|----------------|
| **Positive Value** | Meter reading must be ≥ 0 | "Meter Reading must be a positive number" | Suggest entering a positive value |
| **Valid Date** | Date must not be in the future | "Date cannot be in the future" | Set to current date and time |
| **Chronological Order** | Date must not be earlier than previous reading | "Date must be after previous reading date" | Suggest using a date after the previous reading |
| **Logical Value** | Reading must not exceed previous reading + top-ups | "Reading cannot exceed previous reading plus top-ups" | Suggest checking the value or adding missing top-ups |
| **Required Fields** | Value and date must be provided | "Please enter a value and date" | Highlight empty fields |

### Warning Rules (Generate Warnings)

| Rule | Description | Warning Message | Suggested Action |
|------|-------------|-----------------|-----------------|
| **Unusual Value** | Reading is > 2x recent average | "This reading is unusually high" | Confirm value is correct |
| **Duplicate Date** | Another entry exists with same date | "Another entry exists with this date" | Adjust time by a few minutes |
| **Large Gap** | More than 30 days since last reading | "It's been over 30 days since your last reading" | Consider adding intermediate readings |
| **Zero Value** | Reading is exactly 0 | "Are you sure the meter reading is 0?" | Confirm value is correct |

## Top-up Validation

### Required Rules (Must Pass)

| Rule | Description | Error Message | Recovery Action |
|------|-------------|---------------|----------------|
| **Positive Value** | Top-up amount must be > 0 | "Top-up amount must be greater than zero" | Suggest entering a positive value |
| **Valid Date** | Date must not be in the future | "Date cannot be in the future" | Set to current date and time |
| **Required Fields** | Amount and date must be provided | "Please enter an amount and date" | Highlight empty fields |

### Warning Rules (Generate Warnings)

| Rule | Description | Warning Message | Suggested Action |
|------|-------------|-----------------|-----------------|
| **Unusual Amount** | Amount is > 2x typical top-up | "This top-up is unusually large" | Confirm amount is correct |
| **Duplicate Date** | Another entry exists with same date | "Another entry exists with this date" | Adjust time by a few minutes |
| **Frequent Top-ups** | Multiple top-ups within 24 hours | "You've added multiple top-ups today" | Confirm this is intentional |

## Date Validation

### Required Rules (Must Pass)

| Rule | Description | Error Message | Recovery Action |
|------|-------------|---------------|----------------|
| **Valid Format** | Date must be in valid format | "Invalid date format" | Reset to current date |
| **Not Future** | Date must not be in the future | "Date cannot be in the future" | Set to current date and time |
| **Valid Range** | Date must be after app release date | "Date cannot be before app release" | Suggest using a more recent date |

### Warning Rules (Generate Warnings)

| Rule | Description | Warning Message | Suggested Action |
|------|-------------|-----------------|-----------------|
| **Old Date** | Date is more than 1 year in the past | "This date is over a year old" | Confirm date is correct |
| **Time Mismatch** | Time is outside typical entry hours | "This time is unusual for meter readings" | Confirm time is correct |

## Cross-Entry Validation

### Required Rules (Must Pass)

| Rule | Description | Error Message | Recovery Action |
|------|-------------|---------------|----------------|
| **Logical Sequence** | Meter readings must not increase over time | "Meter readings should decrease over time" | Suggest checking values |
| **Balance Consistency** | Current reading + used electricity must equal previous reading + top-ups | "Your meter balance doesn't add up" | Suggest checking for missing entries |

### Warning Rules (Generate Warnings)

| Rule | Description | Warning Message | Suggested Action |
|------|-------------|-----------------|-----------------|
| **Usage Spike** | Usage is > 3x recent average | "Your usage has increased significantly" | Check for leaks or unusual consumption |
| **Usage Drop** | Usage is < 0.3x recent average | "Your usage has decreased significantly" | Check if meter is working correctly |
| **Missing Top-ups** | Balance suggests missing top-ups | "You may have missing top-up entries" | Add any missing top-ups |

## Validation Implementation

### Real-time Validation

- Validate input as the user types
- Show inline validation messages
- Use color coding to indicate validation status:
  - Red: Error (must be fixed)
  - Yellow: Warning (can proceed but should review)
  - Green: Valid

### Visual Indicators

- Error icon (⚠️) for errors
- Warning icon (⚠️) for warnings
- Check icon (✓) for valid input
- Highlight problematic fields with appropriate colors
- Shake animation for invalid submissions

### Correction Assistance

- Provide specific, actionable error messages
- Offer one-tap fixes for common issues
- Auto-format input where possible (e.g., date formats)
- Suggest valid values based on context

## Validation Dashboard

The app will include a validation dashboard that:

1. Shows all invalid entries in one place
2. Categorizes issues by severity
3. Provides batch correction tools
4. Offers guided fixes for complex issues
5. Tracks validation status over time

## Edge Cases

| Edge Case | Handling Strategy |
|-----------|-------------------|
| **First Entry** | Skip previous reading validation |
| **Multiple Entries Same Day** | Order by timestamp and validate sequence |
| **Daylight Saving Time** | Store dates in UTC to avoid ambiguity |
| **Device Time Change** | Validate against server time if available |
| **Data Import** | Run full validation on imported data |
| **Deleted Entries** | Recalculate validity of dependent entries |

## Implementation Notes

- Implement validation in a separate service for reusability
- Cache validation results to avoid repeated calculations
- Run validation in background for large datasets
- Provide batch validation for imported data
- Log validation failures for analysis
