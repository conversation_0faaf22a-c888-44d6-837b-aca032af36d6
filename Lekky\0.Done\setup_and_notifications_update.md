# NewLekky Setup Screen and Notifications Update

This document provides detailed specifications for the Setup screen and Notifications indication in the NewLekky app, based on the screenshots provided.

## 1. Setup Screen Implementation

### Overview
The Setup screen is a multi-step process that guides users through initial configuration when they first launch the app. It follows a modular architecture with clear separation of concerns.

### Visual Design

#### Header
- **Background Color**: Dark gray (`#424242`)
- **Title**: "Settings" in white text, left-aligned
- **Back Button**: White back arrow icon, right-aligned

#### Content Area
- **Background Color**: Black with subtle gradient
- **Cards**: Light gray with rounded corners (matching the design in the screenshots)
- **Toggle Switches**: Blue when active, purple/gray when inactive (matching the design in the screenshots)

### Setup Flow

1. **Language Selection**
   - Card with globe icon
   - "Language" title with current selection displayed
   - Toggle switch to enable/disable language selection
   - When enabled, shows radio button list of languages:
     - English (default)
     - Spanish
     - French
     - German
     - Italian
     - Portuguese
     - Russian
     - Chinese
     - Japanese
   - "Select your preferred language" helper text

2. **Currency Selection**
   - Card with currency icon
   - "Currency" title with current selection displayed (£ by default)
   - Toggle switch to enable/disable currency selection
   - When enabled, shows list of currency options

3. **Region Settings**
   - Card with globe icon
   - "Region" title
   - Toggle switch to enable/disable region settings
   - Collapsible section containing Language and Currency settings

4. **Initial Meter Reading**
   - Form for entering the initial meter reading
   - Numeric keypad for easy entry
   - Validation to ensure positive value

5. **Alert Configuration**
   - Alert threshold setting (default: 5.0)
   - Days in advance setting (default: 2)
   - Helper text explaining each setting

6. **Date Format Selection**
   - Options for different date formats
   - Preview of selected format

### Implementation Details

#### Architecture
```
features/setup/
├── data/
│   └── setup_repository.dart      # Data access for setup
├── domain/
│   ├── models/
│   │   └── setup_config.dart      # Setup configuration model
│   └── usecases/
│       ├── save_setup.dart        # Save setup logic
│       └── load_setup.dart        # Load setup logic
└── presentation/
    ├── controllers/
    │   └── setup_controller.dart  # Business logic for setup screen
    ├── screens/
    │   └── setup_screen.dart      # Main screen UI (<300 lines)
    └── widgets/
        ├── meter_unit_selector.dart # Meter unit selection UI
        ├── date_format_selector.dart # Date format selection UI
        ├── threshold_input.dart   # Alert threshold input UI
        ├── days_advance_input.dart # Days in advance input UI
        └── setup_actions.dart     # Save and load buttons
```

#### Key Components

1. **SetupConfig Model**
   ```dart
   class SetupConfig {
     final String language;
     final String currency;
     final double initialReading;
     final double alertThreshold;
     final int daysInAdvance;
     final String dateFormat;
     
     // Constructor and methods
   }
   ```

2. **SetupController**
   ```dart
   class SetupController extends ChangeNotifier {
     // State variables
     String _language = 'English';
     String _currency = '£';
     double _initialReading = 0.0;
     double _alertThreshold = 5.0;
     int _daysInAdvance = 2;
     String _dateFormat = 'dd-MM-yyyy';
     int _currentStep = 0;
     
     // Getters and setters
     
     // Methods for saving and loading setup
     Future<void> saveSetup() async {
       // Implementation
     }
     
     // Navigation methods
     void nextStep() {
       if (_currentStep < 5) {
         _currentStep++;
         notifyListeners();
       }
     }
     
     void previousStep() {
       if (_currentStep > 0) {
         _currentStep--;
         notifyListeners();
       }
     }
   }
   ```

3. **SetupScreen**
   ```dart
   class SetupScreen extends StatelessWidget {
     @override
     Widget build(BuildContext context) {
       return ChangeNotifierProvider(
         create: (_) => SetupController(),
         child: Scaffold(
           appBar: AppBar(
             title: const Text('Settings'),
             backgroundColor: AppColors.darkGray,
             leading: Consumer<SetupController>(
               builder: (context, controller, _) {
                 return controller.currentStep > 0
                   ? IconButton(
                       icon: const Icon(Icons.arrow_back),
                       onPressed: () => controller.previousStep(),
                     )
                   : const SizedBox.shrink();
               },
             ),
           ),
           body: Consumer<SetupController>(
             builder: (context, controller, _) {
               return _buildCurrentStep(context, controller);
             },
           ),
         ),
       );
     }
     
     Widget _buildCurrentStep(BuildContext context, SetupController controller) {
       switch (controller.currentStep) {
         case 0: return LanguageSelector(controller: controller);
         case 1: return CurrencySelector(controller: controller);
         case 2: return InitialReadingInput(controller: controller);
         case 3: return AlertConfigInput(controller: controller);
         case 4: return DateFormatSelector(controller: controller);
         case 5: return SetupSummary(controller: controller);
         default: return const SizedBox.shrink();
       }
     }
   }
   ```

4. **Information Text Banner**
   - Displays contextual help text at the top of the screen
   - Changes based on the current setup step
   - Examples:
     - "Total-avg shows usage since your first reading."
     - "Recent-avg shows usage between consecutive readings."
     - "Log meter readings weekly for accurate data."

## 2. Notifications Indication Implementation

### Overview
The notification system in NewLekky includes a visual indication of unread notifications across all main screens (Home, Cost, History). Based on the screenshots, the notification icon in the app bar shows a red badge with the number of unread notifications.

### Visual Design

#### Notification Icon
- **Icon**: Bell icon (outlined when no notifications, filled when there are notifications)
- **Badge**: Red circle with white number indicating unread notification count
- **Position**: In the app bar, aligned with other action icons

### Implementation Details

#### Architecture
```
features/notifications/
├── data/
│   └── notification_repository.dart  # Data access for notifications
├── domain/
│   ├── models/
│   │   └── notification.dart         # Notification model
│   └── usecases/
│       ├── get_notifications.dart    # Get notifications logic
│       └── mark_as_read.dart         # Mark notifications as read
└── presentation/
    ├── controllers/
    │   └── notification_controller.dart  # Business logic
    ├── screens/
    │   └── notification_screen.dart      # Notifications list screen
    └── widgets/
        ├── notification_badge.dart       # Badge indicator widget
        └── notification_item.dart        # Individual notification item
```

#### Key Components

1. **Notification Model**
   ```dart
   class Notification {
     final int id;
     final String title;
     final String message;
     final DateTime timestamp;
     final NotificationType type;
     final bool isRead;
     
     // Constructor and methods
   }
   
   enum NotificationType {
     lowBalance,
     timeToTopUp,
     invalidRecord,
     readingReminder
   }
   ```

2. **NotificationBadge Widget**
   ```dart
   class NotificationBadge extends StatelessWidget {
     final int count;
     final VoidCallback onTap;
     
     const NotificationBadge({
       Key? key,
       required this.count,
       required this.onTap,
     }) : super(key: key);
     
     @override
     Widget build(BuildContext context) {
       return Stack(
         children: [
           IconButton(
             icon: const Icon(
               Icons.notifications_outlined,
               color: Colors.white,
               size: 24,
             ),
             onPressed: onTap,
           ),
           if (count > 0)
             Positioned(
               right: 0,
               top: 0,
               child: Container(
                 padding: const EdgeInsets.all(2),
                 decoration: BoxDecoration(
                   color: Colors.red,
                   borderRadius: BorderRadius.circular(10),
                 ),
                 constraints: const BoxConstraints(
                   minWidth: 16,
                   minHeight: 16,
                 ),
                 child: Text(
                   count.toString(),
                   style: const TextStyle(
                     color: Colors.white,
                     fontSize: 10,
                   ),
                   textAlign: TextAlign.center,
                 ),
               ),
             ),
         ],
       );
     }
   }
   ```

3. **Integration in Screen AppBars**
   ```dart
   List<Widget> _buildAppBarActions(BuildContext context) {
     return [
       Consumer<NotificationController>(
         builder: (context, controller, _) {
           return NotificationBadge(
             count: controller.unreadCount,
             onTap: () => _showNotifications(context),
           );
         },
       ),
       // Other app bar actions
     ];
   }
   ```

### Notification Types and Behavior

1. **Low Balance Alerts**
   - Triggered when meter reading falls below alert threshold
   - Displays red badge on notification icon
   - Shows in notification center with high priority

2. **Time to Top-Up Alerts**
   - Triggered when projected days until threshold is less than days in advance
   - Displays red badge on notification icon
   - Shows in notification center with medium priority

3. **Invalid Records Alerts**
   - Triggered when validation of entries fails
   - Displays red badge on notification icon
   - Shows in notification center with medium priority

4. **Reading Reminders**
   - Scheduled prompts to take meter readings
   - Displays red badge on notification icon
   - Shows in notification center with normal priority

### Notification Center

The notification center will be accessible by tapping the notification icon in the app bar. It will display a list of all notifications, with unread notifications highlighted. Users can mark notifications as read individually or all at once.

## Integration with Existing Screens

Both the Setup screen and Notification indication will be integrated with the existing screens in the NewLekky app, maintaining consistent design language and behavior across the app.
