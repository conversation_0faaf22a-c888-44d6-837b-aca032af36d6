1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.lekky.app"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="34" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->D:\000.Workspace\Lekky\android\app\src\debug\AndroidManifest.xml:6:5-66
15-->D:\000.Workspace\Lekky\android\app\src\debug\AndroidManifest.xml:6:22-64
16    <!-- Add permissions for notifications -->
17    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" /> <!-- Add permission for boot completed to reschedule notifications -->
17-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:4:5-76
17-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:4:22-74
18    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" /> <!-- Add permissions for storage access -->
18-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:6:5-80
18-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:6:22-78
19    <uses-permission
19-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:8:5-106
20        android:name="android.permission.READ_EXTERNAL_STORAGE"
20-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:8:22-77
21        android:maxSdkVersion="32" />
21-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:8:78-104
22    <uses-permission
22-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:9:5-107
23        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
23-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:9:22-78
24        android:maxSdkVersion="29" /> <!-- Add permissions for background work -->
24-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:9:79-105
25    <uses-permission android:name="android.permission.WAKE_LOCK" />
25-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:11:5-67
25-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:11:22-65
26    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
26-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:12:5-76
26-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:12:22-74
27    <!--
28 Required to query activities that can process text, see:
29         https://developer.android.com/training/package-visibility?hl=en and
30         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
31
32         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
33    -->
34    <queries>
34-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:68:5-85:15
35        <intent>
35-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:69:9-72:18
36            <action android:name="android.intent.action.PROCESS_TEXT" />
36-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:70:13-72
36-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:70:21-70
37
38            <data android:mimeType="text/plain" />
38-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:71:13-50
38-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:71:19-48
39        </intent>
40        <!-- For document selection -->
41        <intent>
41-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:74:9-76:18
42            <action android:name="android.intent.action.OPEN_DOCUMENT_TREE" />
42-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:75:13-79
42-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:75:21-76
43        </intent>
44        <intent>
44-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:77:9-80:18
45            <action android:name="android.intent.action.CREATE_DOCUMENT" />
45-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:78:13-76
45-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:78:21-73
46
47            <data android:mimeType="text/csv" />
47-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:71:13-50
47-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:71:19-48
48        </intent>
49        <intent>
49-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:81:9-84:18
50            <action android:name="android.intent.action.OPEN_DOCUMENT" />
50-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:82:13-74
50-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:82:21-71
51
52            <data android:mimeType="text/csv" />
52-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:71:13-50
52-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:71:19-48
53        </intent>
54        <intent>
54-->[:file_picker] D:\000.Workspace\Lekky\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:12:9-16:18
55            <action android:name="android.intent.action.GET_CONTENT" />
55-->[:file_picker] D:\000.Workspace\Lekky\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:13:13-72
55-->[:file_picker] D:\000.Workspace\Lekky\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:13:21-69
56
57            <data android:mimeType="*/*" />
57-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:71:13-50
57-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:71:19-48
58        </intent>
59    </queries>
60
61    <uses-permission android:name="android.permission.VIBRATE" />
61-->[:flutter_local_notifications] D:\000.Workspace\Lekky\build\flutter_local_notifications\intermediates\merged_manifest\debug\AndroidManifest.xml:9:5-66
61-->[:flutter_local_notifications] D:\000.Workspace\Lekky\build\flutter_local_notifications\intermediates\merged_manifest\debug\AndroidManifest.xml:9:22-63
62    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
62-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:24:5-79
62-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:24:22-76
63
64    <permission
64-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\684577351670909f117ab3c5c378ca3b\transformed\core-1.10.1\AndroidManifest.xml:22:5-24:47
65        android:name="com.lekky.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
65-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\684577351670909f117ab3c5c378ca3b\transformed\core-1.10.1\AndroidManifest.xml:23:9-81
66        android:protectionLevel="signature" />
66-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\684577351670909f117ab3c5c378ca3b\transformed\core-1.10.1\AndroidManifest.xml:24:9-44
67
68    <uses-permission android:name="com.lekky.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
68-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\684577351670909f117ab3c5c378ca3b\transformed\core-1.10.1\AndroidManifest.xml:26:5-97
68-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\684577351670909f117ab3c5c378ca3b\transformed\core-1.10.1\AndroidManifest.xml:26:22-94
69
70    <application
71        android:name="android.app.Application"
72        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
72-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\684577351670909f117ab3c5c378ca3b\transformed\core-1.10.1\AndroidManifest.xml:28:18-86
73        android:debuggable="true"
74        android:enableOnBackInvokedCallback="true"
75        android:icon="@mipmap/ic_launcher"
76        android:label="lekky" >
77        <activity
78            android:name="com.lekky.app.MainActivity"
79            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
80            android:exported="true"
81            android:hardwareAccelerated="true"
82            android:launchMode="singleTop"
83            android:theme="@style/LaunchTheme"
84            android:windowSoftInputMode="adjustResize" >
85
86            <!--
87                 Specifies an Android theme to apply to this Activity as soon as
88                 the Android process has started. This theme is visible to the user
89                 while the Flutter UI initializes. After that, this theme continues
90                 to determine the Window background behind the Flutter UI.
91            -->
92            <meta-data
93                android:name="io.flutter.embedding.android.NormalTheme"
94                android:resource="@style/NormalTheme" />
95
96            <intent-filter>
97                <action android:name="android.intent.action.MAIN" />
98
99                <category android:name="android.intent.category.LAUNCHER" />
100            </intent-filter>
101        </activity>
102        <!--
103             Don't delete the meta-data below.
104             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
105        -->
106        <meta-data
107            android:name="flutterEmbedding"
108            android:value="2" />
109
110        <!-- Add receiver for boot completed to reschedule notifications -->
111        <receiver
112            android:name="com.dexterous.flutterlocalnotifications.ScheduledNotificationBootReceiver"
113            android:exported="true" >
114            <intent-filter>
115                <action android:name="android.intent.action.BOOT_COMPLETED" />
115-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:17-79
115-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:25-76
116                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
117            </intent-filter>
118        </receiver>
119
120        <!-- Package name for the application -->
121        <meta-data
122            android:name="com.lekky.app.PACKAGE_NAME"
123            android:value="com.lekky.app" />
124
125        <activity
125-->[:url_launcher_android] D:\000.Workspace\Lekky\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:10:9-13:74
126            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
126-->[:url_launcher_android] D:\000.Workspace\Lekky\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-74
127            android:exported="false"
127-->[:url_launcher_android] D:\000.Workspace\Lekky\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:12:13-37
128            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
128-->[:url_launcher_android] D:\000.Workspace\Lekky\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:13:13-71
129
130        <provider
130-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:29:9-37:20
131            android:name="androidx.startup.InitializationProvider"
131-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:30:13-67
132            android:authorities="com.lekky.app.androidx-startup"
132-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:31:13-68
133            android:exported="false" >
133-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:32:13-37
134            <meta-data
134-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:34:13-36:52
135                android:name="androidx.work.WorkManagerInitializer"
135-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:35:17-68
136                android:value="androidx.startup" />
136-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:36:17-49
137        </provider>
138
139        <service
139-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:39:9-45:35
140            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
140-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:40:13-88
141            android:directBootAware="false"
141-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:41:13-44
142            android:enabled="@bool/enable_system_alarm_service_default"
142-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:42:13-72
143            android:exported="false" />
143-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:43:13-37
144        <service
144-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:46:9-52:35
145            android:name="androidx.work.impl.background.systemjob.SystemJobService"
145-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:47:13-84
146            android:directBootAware="false"
146-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:48:13-44
147            android:enabled="@bool/enable_system_job_service_default"
147-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:49:13-70
148            android:exported="true"
148-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:50:13-36
149            android:permission="android.permission.BIND_JOB_SERVICE" />
149-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:51:13-69
150        <service
150-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:53:9-59:35
151            android:name="androidx.work.impl.foreground.SystemForegroundService"
151-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:54:13-81
152            android:directBootAware="false"
152-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:55:13-44
153            android:enabled="@bool/enable_system_foreground_service_default"
153-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:56:13-77
154            android:exported="false" />
154-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:57:13-37
155
156        <receiver
156-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:61:9-66:35
157            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
157-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:62:13-88
158            android:directBootAware="false"
158-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:63:13-44
159            android:enabled="true"
159-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:64:13-35
160            android:exported="false" />
160-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:65:13-37
161        <receiver
161-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:67:9-77:20
162            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
162-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:68:13-106
163            android:directBootAware="false"
163-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:69:13-44
164            android:enabled="false"
164-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:70:13-36
165            android:exported="false" >
165-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:71:13-37
166            <intent-filter>
166-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:73:13-76:29
167                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
167-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:17-87
167-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:25-84
168                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
168-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:17-90
168-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:25-87
169            </intent-filter>
170        </receiver>
171        <receiver
171-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:78:9-88:20
172            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
172-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:79:13-104
173            android:directBootAware="false"
173-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:80:13-44
174            android:enabled="false"
174-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:81:13-36
175            android:exported="false" >
175-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:82:13-37
176            <intent-filter>
176-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:84:13-87:29
177                <action android:name="android.intent.action.BATTERY_OKAY" />
177-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:17-77
177-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:25-74
178                <action android:name="android.intent.action.BATTERY_LOW" />
178-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:17-76
178-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:25-73
179            </intent-filter>
180        </receiver>
181        <receiver
181-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:89:9-99:20
182            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
182-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:90:13-104
183            android:directBootAware="false"
183-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:91:13-44
184            android:enabled="false"
184-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:92:13-36
185            android:exported="false" >
185-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:93:13-37
186            <intent-filter>
186-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:95:13-98:29
187                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
187-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:17-83
187-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:25-80
188                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
188-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:17-82
188-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:25-79
189            </intent-filter>
190        </receiver>
191        <receiver
191-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:100:9-109:20
192            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
192-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:101:13-103
193            android:directBootAware="false"
193-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:102:13-44
194            android:enabled="false"
194-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:103:13-36
195            android:exported="false" >
195-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:104:13-37
196            <intent-filter>
196-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:106:13-108:29
197                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
197-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:17-79
197-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:25-76
198            </intent-filter>
199        </receiver>
200        <receiver
200-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:110:9-121:20
201            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
201-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:111:13-88
202            android:directBootAware="false"
202-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:112:13-44
203            android:enabled="false"
203-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:113:13-36
204            android:exported="false" >
204-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:114:13-37
205            <intent-filter>
205-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:116:13-120:29
206                <action android:name="android.intent.action.BOOT_COMPLETED" />
206-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:17-79
206-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:25-76
207                <action android:name="android.intent.action.TIME_SET" />
207-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:17-73
207-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:25-70
208                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
208-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:17-81
208-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:25-78
209            </intent-filter>
210        </receiver>
211        <receiver
211-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:122:9-131:20
212            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
212-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:123:13-99
213            android:directBootAware="false"
213-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:124:13-44
214            android:enabled="@bool/enable_system_alarm_service_default"
214-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:125:13-72
215            android:exported="false" >
215-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:126:13-37
216            <intent-filter>
216-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:128:13-130:29
217                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
217-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:17-98
217-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:25-95
218            </intent-filter>
219        </receiver>
220        <receiver
220-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:132:9-142:20
221            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
221-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:133:13-78
222            android:directBootAware="false"
222-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:134:13-44
223            android:enabled="true"
223-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:135:13-35
224            android:exported="true"
224-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:136:13-36
225            android:permission="android.permission.DUMP" >
225-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:137:13-57
226            <intent-filter>
226-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:139:13-141:29
227                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
227-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:17-88
227-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:25-85
228            </intent-filter>
229        </receiver>
230
231        <service
231-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\00ee8946583275229f58c64a5ecbb412\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
232            android:name="androidx.room.MultiInstanceInvalidationService"
232-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\00ee8946583275229f58c64a5ecbb412\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
233            android:directBootAware="true"
233-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\00ee8946583275229f58c64a5ecbb412\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
234            android:exported="false" />
234-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\00ee8946583275229f58c64a5ecbb412\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
235
236        <uses-library
236-->[androidx.window:window:1.0.0-beta04] C:\Users\<USER>\.gradle\caches\transforms-3\d8adab4e681d0863a25dfe4be8db95bf\transformed\jetified-window-1.0.0-beta04\AndroidManifest.xml:25:9-27:40
237            android:name="androidx.window.extensions"
237-->[androidx.window:window:1.0.0-beta04] C:\Users\<USER>\.gradle\caches\transforms-3\d8adab4e681d0863a25dfe4be8db95bf\transformed\jetified-window-1.0.0-beta04\AndroidManifest.xml:26:13-54
238            android:required="false" />
238-->[androidx.window:window:1.0.0-beta04] C:\Users\<USER>\.gradle\caches\transforms-3\d8adab4e681d0863a25dfe4be8db95bf\transformed\jetified-window-1.0.0-beta04\AndroidManifest.xml:27:13-37
239        <uses-library
239-->[androidx.window:window:1.0.0-beta04] C:\Users\<USER>\.gradle\caches\transforms-3\d8adab4e681d0863a25dfe4be8db95bf\transformed\jetified-window-1.0.0-beta04\AndroidManifest.xml:28:9-30:40
240            android:name="androidx.window.sidecar"
240-->[androidx.window:window:1.0.0-beta04] C:\Users\<USER>\.gradle\caches\transforms-3\d8adab4e681d0863a25dfe4be8db95bf\transformed\jetified-window-1.0.0-beta04\AndroidManifest.xml:29:13-51
241            android:required="false" />
241-->[androidx.window:window:1.0.0-beta04] C:\Users\<USER>\.gradle\caches\transforms-3\d8adab4e681d0863a25dfe4be8db95bf\transformed\jetified-window-1.0.0-beta04\AndroidManifest.xml:30:13-37
242    </application>
243
244</manifest>
