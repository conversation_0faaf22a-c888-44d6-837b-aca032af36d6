// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'settings_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$settingsHash() => r'9f8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c';

/// Provider for comprehensive settings management using Riverpod
///
/// Copied from [Settings].
@ProviderFor(Settings)
final settingsProvider = AutoDisposeAsyncNotifierProvider<Settings, SettingsState>.internal(
  Settings.new,
  name: r'settingsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product') ? null : _$settingsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$Settings = AutoDisposeAsyncNotifier<SettingsState>;
