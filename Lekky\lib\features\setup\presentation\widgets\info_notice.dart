import 'package:flutter/material.dart';

/// A widget for displaying information notices in the setup screen
class InfoNotice extends StatelessWidget {
  /// Message to display
  final String message;
  
  /// Icon to display
  final IconData icon;
  
  /// Background color
  final Color? backgroundColor;
  
  /// Text color
  final Color? textColor;
  
  /// Constructor
  const InfoNotice({
    Key? key,
    required this.message,
    this.icon = Icons.info_outline,
    this.backgroundColor,
    this.textColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final bgColor = backgroundColor ?? Colors.amber.shade100;
    final txtColor = textColor ?? Colors.amber.shade900;
    
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12),
      margin: const EdgeInsets.symmetric(vertical: 8),
      decoration: BoxDecoration(
        color: bgColor,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            color: txtColor,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              message,
              style: TextStyle(
                color: txtColor,
                fontSize: 14,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
