import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/providers/settings_provider.dart';
import '../../../../core/shared/widgets/settings_toggle.dart';
import '../../../notifications/presentation/dialogs/notification_dialog.dart';

/// Notifications settings screen
class NotificationsScreen extends ConsumerWidget {
  /// Constructor
  const NotificationsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final settingsAsync = ref.watch(settingsProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Alerts & Notifications'),
        backgroundColor: const Color(0xFF424242),
        foregroundColor: Colors.white,
      ),
      body: settingsAsync.when(
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => Center(
          child: Text('Error loading settings: $error'),
        ),
        data: (settings) => ListView(
          padding: const EdgeInsets.all(16.0),
          children: [
            // Alert threshold card
            Card(
              margin: const EdgeInsets.only(bottom: 16.0),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Row(
                      children: [
                        Icon(Icons.warning, color: Colors.amber),
                        SizedBox(width: 16),
                        Text(
                          'Alert Threshold',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      'Set the threshold for low balance alerts. You will be notified when your balance falls below this amount.',
                      style: TextStyle(
                        fontSize: 14,
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Alert threshold slider
                    Row(
                      children: [
                        Text('${settings.currencySymbol}0'),
                        Expanded(
                          child: Slider(
                            value: settings.alertThreshold,
                            min: 0,
                            max: 20,
                            divisions: 40,
                            label:
                                '${settings.currencySymbol}${settings.alertThreshold.toStringAsFixed(2)}',
                            onChanged: (value) {
                              ref
                                  .read(settingsProvider.notifier)
                                  .updateAlertThreshold(value);
                            },
                          ),
                        ),
                        Text('${settings.currencySymbol}20'),
                      ],
                    ),

                    Center(
                      child: Text(
                        'Current: ${settings.currencySymbol}${settings.alertThreshold.toStringAsFixed(2)}',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),

            // Days in advance card
            Card(
              margin: const EdgeInsets.only(bottom: 16.0),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Row(
                      children: [
                        Icon(Icons.calendar_today, color: Colors.blue),
                        SizedBox(width: 16),
                        Text(
                          'Days in Advance',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      'Set how many days in advance you want to be notified about low balance.',
                      style: TextStyle(
                        fontSize: 14,
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Days in advance slider
                    Row(
                      children: [
                        const Text('1 day'),
                        Expanded(
                          child: Slider(
                            value: settings.daysInAdvance.toDouble(),
                            min: 1,
                            max: 99,
                            divisions: 98,
                            label: '${settings.daysInAdvance} days',
                            onChanged: (value) {
                              ref
                                  .read(settingsProvider.notifier)
                                  .updateDaysInAdvance(value.toInt());
                            },
                          ),
                        ),
                        const Text('99 days'),
                      ],
                    ),

                    Center(
                      child: Text(
                        'Current: ${settings.daysInAdvance} days',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),

            // View all notifications card
            Card(
              margin: const EdgeInsets.only(bottom: 16.0),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Row(
                      children: [
                        Icon(Icons.notifications_active, color: Colors.blue),
                        SizedBox(width: 16),
                        Text(
                          'Notification Center',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      'View all your notifications in one place.',
                      style: TextStyle(
                        fontSize: 14,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Center(
                      child: ElevatedButton.icon(
                        icon: const Icon(Icons.notifications),
                        label: const Text('View All Notifications'),
                        onPressed: () {
                          showNotificationDialog(context);
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),

            // Notification types card
            Card(
              margin: const EdgeInsets.only(bottom: 16.0),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Row(
                      children: [
                        Icon(Icons.notifications, color: Colors.blue),
                        SizedBox(width: 16),
                        Text(
                          'Notification Types',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),

                    // Notifications master toggle
                    SettingsToggle(
                      title: 'Enable Notifications',
                      description: 'Turn on/off all notifications',
                      value: settings.notificationsEnabled,
                      onChanged: (value) {
                        ref
                            .read(settingsProvider.notifier)
                            .updateNotificationsEnabled(value);
                      },
                      icon: Icons.notifications,
                    ),

                    const Divider(),

                    // Only show these options if notifications are enabled
                    if (settings.notificationsEnabled) ...[
                      // Reminders toggle
                      SettingsToggle(
                        title: 'Reminders',
                        description: 'Remind you to take meter readings',
                        value: settings.remindersEnabled,
                        onChanged: (value) {
                          ref
                              .read(settingsProvider.notifier)
                              .updateRemindersEnabled(value);
                        },
                        icon: Icons.alarm,
                      ),

                      // Low balance alerts toggle
                      SettingsToggle(
                        title: 'Low Balance Alerts',
                        description:
                            'Alert when you have less than 24 hours of credit remaining',
                        value: settings.lowBalanceAlertsEnabled,
                        onChanged: (value) {
                          ref
                              .read(settingsProvider.notifier)
                              .updateLowBalanceAlertsEnabled(value);
                        },
                        icon: Icons.money_off,
                      ),

                      // Time to top up alerts toggle
                      SettingsToggle(
                        title: 'Time to Top-up Alerts',
                        description:
                            'Alert when your alert threshold will be reached in your specified days in advance',
                        value: settings.timeToTopUpAlertsEnabled,
                        onChanged: (value) {
                          ref
                              .read(settingsProvider.notifier)
                              .updateTimeToTopUpAlertsEnabled(value);
                        },
                        icon: Icons.access_time,
                      ),

                      // Invalid record alerts toggle
                      SettingsToggle(
                        title: 'Invalid Record Alerts',
                        description: 'Alert when a record is invalid',
                        value: settings.invalidRecordAlertsEnabled,
                        onChanged: (value) {
                          ref
                              .read(settingsProvider.notifier)
                              .updateInvalidRecordAlertsEnabled(value);
                        },
                        icon: Icons.error,
                      ),
                    ],
                  ],
                ),
              ),
            ),

            // Reminder frequency card
            if (settings.notificationsEnabled && settings.remindersEnabled)
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Row(
                        children: [
                          Icon(Icons.repeat, color: Colors.blue),
                          SizedBox(width: 16),
                          Text(
                            'Reminder Frequency',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        'How often do you want to be reminded to take meter readings?',
                        style: TextStyle(
                          fontSize: 14,
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Reminder frequency radio buttons
                      RadioListTile<String>(
                        title: const Text('Daily'),
                        value: 'daily',
                        groupValue: settings.reminderFrequency,
                        onChanged: (value) {
                          if (value != null) {
                            ref
                                .read(settingsProvider.notifier)
                                .updateReminderFrequency(value);
                          }
                        },
                      ),
                      RadioListTile<String>(
                        title: const Text('Weekly'),
                        value: 'weekly',
                        groupValue: settings.reminderFrequency,
                        onChanged: (value) {
                          if (value != null) {
                            ref
                                .read(settingsProvider.notifier)
                                .updateReminderFrequency(value);
                          }
                        },
                      ),
                      RadioListTile<String>(
                        title: const Text('Bi-weekly'),
                        value: 'bi-weekly',
                        groupValue: settings.reminderFrequency,
                        onChanged: (value) {
                          if (value != null) {
                            ref
                                .read(settingsProvider.notifier)
                                .updateReminderFrequency(value);
                          }
                        },
                      ),
                      RadioListTile<String>(
                        title: const Text('Monthly'),
                        value: 'monthly',
                        groupValue: settings.reminderFrequency,
                        onChanged: (value) {
                          if (value != null) {
                            ref
                                .read(settingsProvider.notifier)
                                .updateReminderFrequency(value);
                          }
                        },
                      ),
                    ],
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
