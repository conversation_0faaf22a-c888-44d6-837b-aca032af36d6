import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/providers/settings_provider.dart';
import '../../../../core/models/settings_state.dart';
import '../../../../core/shared/widgets/settings_section_header.dart';
import 'package:flutter/services.dart';

/// Days in Advance settings screen
class DaysAdvanceScreen extends ConsumerWidget {
  /// Constructor
  const DaysAdvanceScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Days in Advance'),
        backgroundColor: const Color(0xFF424242),
        foregroundColor: Colors.white,
      ),
      body: ref.watch(settingsProvider).when(
            loading: () => const Center(child: CircularProgressIndicator()),
            error: (error, stack) => Center(child: Text('Error: $error')),
            data: (settings) {
              return SingleChildScrollView(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Days in advance card
                    Card(
                      margin: const EdgeInsets.all(8.0),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12.0),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const SettingsSectionHeader(
                              title: 'Days in Advance',
                              description:
                                  'Configure when you want to receive usage alerts.',
                              icon: Icons.calendar_today,
                            ),
                            const SizedBox(height: 16),
                            const Text(
                              'Get notified this many days before you run out.',
                              style: TextStyle(fontSize: 14),
                            ),
                            const SizedBox(height: 8),
                            _buildDaysInAdvanceInput(context, settings, ref),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
    );
  }

  Widget _buildDaysInAdvanceInput(
      BuildContext context, SettingsState settings, WidgetRef ref) {
    final textController = TextEditingController(
      text: settings.daysInAdvance.toString(),
    );

    return TextField(
      controller: textController,
      keyboardType: TextInputType.number,
      inputFormatters: [
        FilteringTextInputFormatter.digitsOnly,
      ],
      decoration: const InputDecoration(
        labelText: 'Days in Advance',
        hintText: 'Enter days',
        suffixText: 'days',
        border: OutlineInputBorder(),
        contentPadding: EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 12,
        ),
        helperText: 'Must be between 1 and 99 days',
      ),
      onChanged: (value) {
        if (value.isNotEmpty) {
          final newValue = int.tryParse(value);
          if (newValue != null && newValue >= 1 && newValue <= 99) {
            ref.read(settingsProvider.notifier).updateDaysInAdvance(newValue);
          }
        }
      },
      showCursor: true,
      autofocus: false,
    );
  }
}
