import 'package:freezed_annotation/freezed_annotation.dart';

part 'localization_state.freezed.dart';
part 'localization_state.g.dart';

/// Immutable state model for localization configuration
@freezed
class LocalizationState with _$LocalizationState {
  const factory LocalizationState({
    /// Current language name
    @Default('English') String language,
    
    /// Current language code
    @Default('en') String languageCode,
    
    /// Whether the language is right-to-left
    @Default(false) bool isRTL,
  }) = _LocalizationState;

  factory LocalizationState.fromJson(Map<String, dynamic> json) =>
      _$LocalizationStateFromJson(json);
}

/// Extension methods for LocalizationState
extension LocalizationStateExtension on LocalizationState {
  /// Get display name with flag emoji
  String get displayNameWithFlag {
    switch (languageCode) {
      case 'en':
        return '🇺🇸 English';
      case 'es':
        return '🇪🇸 Spanish';
      case 'fr':
        return '🇫🇷 French';
      case 'de':
        return '🇩🇪 German';
      case 'it':
        return '🇮🇹 Italian';
      case 'pt':
        return '🇵🇹 Portuguese';
      case 'ru':
        return '🇷🇺 Russian';
      case 'zh':
        return '🇨🇳 Chinese';
      case 'ja':
        return '🇯🇵 Japanese';
      case 'hi':
        return '🇮🇳 Hindi';
      case 'ar':
        return '🇸🇦 Arabic';
      case 'he':
        return '🇮🇱 Hebrew';
      default:
        return '🇺🇸 English';
    }
  }
  
  /// Get flag emoji for the language
  String get flagEmoji {
    switch (languageCode) {
      case 'en':
        return '🇺🇸';
      case 'es':
        return '🇪🇸';
      case 'fr':
        return '🇫🇷';
      case 'de':
        return '🇩🇪';
      case 'it':
        return '🇮🇹';
      case 'pt':
        return '🇵🇹';
      case 'ru':
        return '🇷🇺';
      case 'zh':
        return '🇨🇳';
      case 'ja':
        return '🇯🇵';
      case 'hi':
        return '🇮🇳';
      case 'ar':
        return '🇸🇦';
      case 'he':
        return '🇮🇱';
      default:
        return '🇺🇸';
    }
  }
  
  /// Check if language is supported
  bool get isSupported {
    const supportedCodes = [
      'en', 'es', 'fr', 'de', 'it', 'pt', 
      'ru', 'zh', 'ja', 'hi', 'ar', 'he'
    ];
    return supportedCodes.contains(languageCode);
  }
  
  /// Get text direction based on RTL
  String get textDirection => isRTL ? 'rtl' : 'ltr';
}
