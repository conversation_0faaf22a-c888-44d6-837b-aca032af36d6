// File: test/features/widgets/entry_card_test.dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:lekky/core/widgets/app_card.dart';
import '../../helpers/test_data_helper.dart';

void main() {
  group('EntryCard Widget Tests', () {
    testWidgets('should display meter reading entry details',
        (WidgetTester tester) async {
      // Arrange
      final entry = TestDataHelper.createMeterReading(
        id: 1,
        date: DateTime(2023, 1, 1),
        reading: 100.0,
      );

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: AppCard(
              child: Column(
                children: [
                  Text('Reading: ${entry.reading}'),
                  Text(
                      'Date: ${entry.date.day}/${entry.date.month}/${entry.date.year}'),
                  Text(
                      'Type: ${entry.typeCode == 0 ? "Meter Reading" : "Top-up"}'),
                ],
              ),
            ),
          ),
        ),
      );

      // Assert
      expect(find.text('Reading: 100.0'), findsOneWidget);
      expect(find.text('Date: 1/1/2023'), findsOneWidget);
      expect(find.text('Type: Meter Reading'), findsOneWidget);
    });

    testWidgets('should display top-up entry details',
        (WidgetTester tester) async {
      // Arrange
      final entry = TestDataHelper.createTopUp(
        id: 2,
        date: DateTime(2023, 1, 5),
        amountToppedUp: 20.0,
      );

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: AppCard(
              child: Column(
                children: [
                  Text('Amount: ${entry.amountToppedUp}'),
                  Text(
                      'Date: ${entry.date.day}/${entry.date.month}/${entry.date.year}'),
                  Text(
                      'Type: ${entry.typeCode == 0 ? "Meter Reading" : "Top-up"}'),
                ],
              ),
            ),
          ),
        ),
      );

      // Assert
      expect(find.text('Amount: 20.0'), findsOneWidget);
      expect(find.text('Date: 5/1/2023'), findsOneWidget);
      expect(find.text('Type: Top-up'), findsOneWidget);
    });

    testWidgets('should handle tap interactions', (WidgetTester tester) async {
      // Arrange
      bool tapped = false;
      final entry = TestDataHelper.createMeterReading(
        id: 1,
        date: DateTime(2023, 1, 1),
        reading: 100.0,
      );

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: GestureDetector(
              onTap: () => tapped = true,
              child: AppCard(
                child: Text('Reading: ${entry.reading}'),
              ),
            ),
          ),
        ),
      );

      await tester.tap(find.byType(AppCard));
      await tester.pump();

      // Assert
      expect(tapped, isTrue);
    });

    testWidgets('should display validation indicators',
        (WidgetTester tester) async {
      // Arrange
      final invalidEntry = TestDataHelper.createMeterReading(
        id: 1,
        date: DateTime.now().add(const Duration(days: 1)), // Future date
        reading: 100.0,
      );

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: AppCard(
              child: Row(
                children: [
                  Text('Reading: ${invalidEntry.reading}'),
                  const Icon(Icons.warning,
                      color: Colors.red), // Validation indicator
                ],
              ),
            ),
          ),
        ),
      );

      // Assert
      expect(find.byIcon(Icons.warning), findsOneWidget);
      expect(
          find.byWidgetPredicate(
            (widget) => widget is Icon && widget.color == Colors.red,
          ),
          findsOneWidget);
    });

    testWidgets('should apply correct styling for different entry types',
        (WidgetTester tester) async {
      // Arrange
      final meterReading = TestDataHelper.createMeterReading(
        id: 1,
        date: DateTime(2023, 1, 1),
        reading: 100.0,
      );

      final topUp = TestDataHelper.createTopUp(
        id: 2,
        date: DateTime(2023, 1, 5),
        amountToppedUp: 20.0,
      );

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Column(
              children: [
                AppCard(
                  child: Container(
                    decoration: BoxDecoration(
                      border: Border(
                        left: BorderSide(
                          width: 4,
                          color: meterReading.typeCode == 0
                              ? Colors.blue
                              : Colors.green,
                        ),
                      ),
                    ),
                    child: Text('Reading: ${meterReading.reading}'),
                  ),
                ),
                AppCard(
                  child: Container(
                    decoration: BoxDecoration(
                      border: Border(
                        left: BorderSide(
                          width: 4,
                          color:
                              topUp.typeCode == 0 ? Colors.blue : Colors.green,
                        ),
                      ),
                    ),
                    child: Text('Top-up: ${topUp.amountToppedUp}'),
                  ),
                ),
              ],
            ),
          ),
        ),
      );

      // Assert
      expect(find.byType(AppCard), findsNWidgets(2));
      expect(find.text('Reading: 100.0'), findsOneWidget);
      expect(find.text('Top-up: 20.0'), findsOneWidget);
    });

    testWidgets('should handle empty or null values gracefully',
        (WidgetTester tester) async {
      // Arrange
      final entryWithZeroReading = TestDataHelper.createMeterReading(
        id: 1,
        date: DateTime(2023, 1, 1),
        reading: 0.0,
      );

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: AppCard(
              child: Text('Reading: ${entryWithZeroReading.reading}'),
            ),
          ),
        ),
      );

      // Assert
      expect(find.text('Reading: 0.0'), findsOneWidget);
    });

    testWidgets('should be accessible with proper semantics',
        (WidgetTester tester) async {
      // Arrange
      final entry = TestDataHelper.createMeterReading(
        id: 1,
        date: DateTime(2023, 1, 1),
        reading: 100.0,
      );

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Semantics(
              label: 'Meter reading entry',
              child: AppCard(
                child: Text('Reading: ${entry.reading}'),
              ),
            ),
          ),
        ),
      );

      // Assert
      expect(find.bySemanticsLabel('Meter reading entry'), findsOneWidget);
    });
  });
}
