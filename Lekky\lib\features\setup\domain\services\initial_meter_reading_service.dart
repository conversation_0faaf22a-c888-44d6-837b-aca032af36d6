import '../../../../core/utils/logger.dart';
import '../../../../core/utils/event_bus.dart';
import '../../../meter_readings/domain/models/meter_reading.dart';
import '../../../meter_readings/domain/repositories/meter_reading_repository.dart';

/// Service for creating the initial meter reading during setup completion
class InitialMeterReadingService {
  final MeterReadingRepository _meterReadingRepository;

  InitialMeterReadingService(this._meterReadingRepository);

  /// Create initial meter reading from setup preferences
  /// Returns true if successful, false if failed
  Future<bool> createInitialMeterReading(
    double value,
    DateTime setupCompletionTime,
  ) async {
    try {
      // Validate the initial meter reading value
      if (!_validateInitialReading(value)) {
        Logger.error(
            'InitialMeterReadingService: Invalid initial reading value: $value');
        return false;
      }

      // Create the meter reading
      final meterReading = MeterReading(
        value: value,
        date: setupCompletionTime,
        notes: 'Initial meter reading from setup',
      );

      // Add to repository
      final result = await _meterReadingRepository.addMeterReading(meterReading);

      if (result > 0) {
        Logger.info(
            'InitialMeterReadingService: Successfully created initial meter reading with value $value');
        
        // Fire data updated event to refresh UI
        EventBus().fire(EventType.dataUpdated);
        
        return true;
      } else {
        Logger.error(
            'InitialMeterReadingService: Failed to create initial meter reading - repository returned $result');
        return false;
      }
    } catch (e) {
      Logger.error(
          'InitialMeterReadingService: Exception creating initial meter reading: $e');
      return false;
    }
  }

  /// Validate initial meter reading value
  /// For first meter reading, only requirement is positive value up to 999.99
  bool _validateInitialReading(double value) {
    return value >= 0.0 && value <= 999.99;
  }
}
