import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// A reusable widget for currency input fields that automatically selects all text when focused
class CurrencyInputField extends StatefulWidget {
  /// The current value of the field
  final double? value;

  /// Callback when the value changes
  final ValueChanged<double?> onChanged;

  /// Currency symbol to display as prefix
  final String currencySymbol;

  /// Label text for the field
  final String labelText;

  /// Hint text for the field
  final String? hintText;

  /// Helper text for the field
  final String? helperText;

  /// Error text for the field
  final String? errorText;

  /// Whether to allow null values
  final bool allowNull;

  /// Minimum allowed value
  final double minValue;

  /// Maximum allowed value
  final double maxValue;

  /// Number of decimal places to display
  final int decimalPlaces;

  /// Border radius for the input field
  final BorderRadius? borderRadius;

  /// Constructor
  const CurrencyInputField({
    super.key,
    required this.value,
    required this.onChanged,
    required this.currencySymbol,
    required this.labelText,
    this.hintText,
    this.helperText,
    this.errorText,
    this.allowNull = false,
    this.minValue = 0.0,
    this.maxValue = 999.99,
    this.decimalPlaces = 2,
    this.borderRadius,
  });

  @override
  State<CurrencyInputField> createState() => _CurrencyInputFieldState();
}

class _CurrencyInputFieldState extends State<CurrencyInputField> {
  late TextEditingController _controller;
  late FocusNode _focusNode;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(
      text: widget.value?.toStringAsFixed(widget.decimalPlaces) ??
          (widget.allowNull ? '' : '0.00'),
    );
    _focusNode = FocusNode()
      ..addListener(() {
        if (_focusNode.hasFocus) {
          _controller.selection = TextSelection(
            baseOffset: 0,
            extentOffset: _controller.text.length,
          );
        }
      });
  }

  @override
  void didUpdateWidget(covariant CurrencyInputField oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Only update the controller text if the external value changed and we're not focused
    if (!_focusNode.hasFocus &&
        ((widget.value != oldWidget.value) ||
            (widget.value == null && oldWidget.value != null) ||
            (widget.value != null && oldWidget.value == null))) {
      _controller.text = widget.value?.toStringAsFixed(widget.decimalPlaces) ??
          (widget.allowNull ? '' : '0.00');
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final effectiveBorderRadius =
        widget.borderRadius ?? BorderRadius.circular(8);

    return TextField(
      controller: _controller,
      focusNode: _focusNode,
      keyboardType: const TextInputType.numberWithOptions(decimal: true),
      inputFormatters: [
        FilteringTextInputFormatter.allow(
          RegExp(r'^\d{1,3}(\.?\d{0,2})?$'),
        ),
      ],
      decoration: InputDecoration(
        labelText: widget.labelText,
        hintText: widget.hintText,
        prefixText: widget.currencySymbol,
        border: OutlineInputBorder(
          borderRadius: effectiveBorderRadius,
        ),
        errorText: widget.errorText,
        helperText: widget.helperText ??
            'Must be between ${widget.currencySymbol}${widget.minValue.toStringAsFixed(widget.decimalPlaces)} and ${widget.currencySymbol}${widget.maxValue.toStringAsFixed(widget.decimalPlaces)}',
        contentPadding:
            const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      ),
      onChanged: (value) {
        if (value.isEmpty) {
          if (widget.allowNull) {
            widget.onChanged(null);
          } else {
            widget.onChanged(0.0);
          }
        } else {
          final parsedValue = double.tryParse(value);
          if (parsedValue != null &&
              parsedValue >= widget.minValue &&
              parsedValue <= widget.maxValue) {
            widget.onChanged(parsedValue);
          }
        }
      },
      showCursor: true,
      autofocus: false,
    );
  }
}
