import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../../core/shared/enums/entry_enums.dart';
import '../../../../core/widgets/lekky_button.dart';

/// Dialog for filtering history entries
class HistoryFilterDialog extends StatefulWidget {
  /// Current filter type
  final EntryFilterType filterType;

  /// Current sort order
  final EntrySortOrder sortOrder;

  /// Current start date
  final DateTime? startDate;

  /// Current end date
  final DateTime? endDate;

  /// Constructor
  const HistoryFilterDialog({
    Key? key,
    required this.filterType,
    required this.sortOrder,
    this.startDate,
    this.endDate,
  }) : super(key: key);

  @override
  State<HistoryFilterDialog> createState() => _HistoryFilterDialogState();
}

class _HistoryFilterDialogState extends State<HistoryFilterDialog> {
  late EntryFilterType _filterType;
  late EntrySortOrder _sortOrder;
  DateTime? _startDate;
  DateTime? _endDate;

  @override
  void initState() {
    super.initState();
    _filterType = widget.filterType;
    _sortOrder = widget.sortOrder;
    _startDate = widget.startDate;
    _endDate = widget.endDate;
  }

  /// Calculate responsive dialog width
  double _getDialogWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    return screenWidth < 600 ? screenWidth * 0.95 : 500.0;
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final dialogWidth = _getDialogWidth(context);
    final horizontalPadding = (screenWidth - dialogWidth) / 2;

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      elevation: 24,
      insetPadding: EdgeInsets.symmetric(
        horizontal: horizontalPadding,
        vertical: 28,
      ),
      child: Container(
        width: dialogWidth,
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDialogHeader(context),
            const SizedBox(height: 24),
            _buildFilterTypeDropdown(context),
            const SizedBox(height: 16),
            _buildSortOrderDropdown(context),
            const SizedBox(height: 16),
            _buildDateRangeSelector(context),
            const SizedBox(height: 32),
            _buildActionButtons(context),
          ],
        ),
      ),
    );
  }

  /// Build dialog header
  Widget _buildDialogHeader(BuildContext context) {
    final theme = Theme.of(context);

    return Row(
      children: [
        Icon(
          Icons.tune,
          color: theme.colorScheme.primary,
          size: 24,
        ),
        const SizedBox(width: 8),
        Text(
          'Filter Entries',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.w500,
            color: theme.colorScheme.onSurface,
          ),
        ),
        const Spacer(),
        IconButton(
          icon: const Icon(Icons.close),
          onPressed: () => Navigator.of(context).pop(),
          tooltip: 'Close',
        ),
      ],
    );
  }

  /// Build filter type dropdown
  Widget _buildFilterTypeDropdown(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Entry Type',
          style: TextStyle(
            fontSize: 12,
            color: theme.colorScheme.onSurface.withOpacity(0.6),
          ),
        ),
        const SizedBox(height: 4),
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: theme.colorScheme.outline),
            borderRadius: BorderRadius.circular(8),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<EntryFilterType>(
              value: _filterType,
              isExpanded: true,
              padding: const EdgeInsets.symmetric(horizontal: 12),
              borderRadius: BorderRadius.circular(8),
              items: EntryFilterType.values.map((type) {
                String label;
                switch (type) {
                  case EntryFilterType.all:
                    label = 'All Entries';
                    break;
                  case EntryFilterType.meterReadings:
                    label = 'Meter Readings';
                    break;
                  case EntryFilterType.topUps:
                    label = 'Top-ups';
                    break;
                  case EntryFilterType.invalid:
                    label = 'Invalid Entries';
                    break;
                }

                if (type == EntryFilterType.invalid) {
                  return DropdownMenuItem<EntryFilterType>(
                    value: type,
                    child: Row(
                      children: [
                        const Icon(
                          Icons.warning_amber,
                          color: Colors.amber,
                          size: 16,
                        ),
                        const SizedBox(width: 8),
                        Text(label),
                      ],
                    ),
                  );
                }

                return DropdownMenuItem<EntryFilterType>(
                  value: type,
                  child: Text(label),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _filterType = value;
                  });
                }
              },
            ),
          ),
        ),
      ],
    );
  }

  /// Build sort order dropdown
  Widget _buildSortOrderDropdown(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Sort',
          style: TextStyle(
            fontSize: 12,
            color: theme.colorScheme.onSurface.withOpacity(0.6),
          ),
        ),
        const SizedBox(height: 4),
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: theme.colorScheme.outline),
            borderRadius: BorderRadius.circular(8),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<EntrySortOrder>(
              value: _sortOrder,
              isExpanded: true,
              padding: const EdgeInsets.symmetric(horizontal: 12),
              borderRadius: BorderRadius.circular(8),
              items: EntrySortOrder.values.map((order) {
                String label;
                switch (order) {
                  case EntrySortOrder.newestFirst:
                    label = 'Newest First';
                    break;
                  case EntrySortOrder.oldestFirst:
                    label = 'Oldest First';
                    break;
                }

                return DropdownMenuItem<EntrySortOrder>(
                  value: order,
                  child: Text(label),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _sortOrder = value;
                  });
                }
              },
            ),
          ),
        ),
      ],
    );
  }

  /// Build date range selector
  Widget _buildDateRangeSelector(BuildContext context) {
    final theme = Theme.of(context);
    final dateFormat = DateFormat('MMM dd, yyyy');

    String dateRangeText;
    if (_startDate != null && _endDate != null) {
      dateRangeText = '${dateFormat.format(_startDate!)} - ${dateFormat.format(_endDate!)}';
    } else {
      dateRangeText = 'Select date range';
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Date Range',
          style: TextStyle(
            fontSize: 12,
            color: theme.colorScheme.onSurface.withOpacity(0.6),
          ),
        ),
        const SizedBox(height: 4),
        InkWell(
          onTap: () => _selectDateRange(context),
          borderRadius: BorderRadius.circular(8),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
            decoration: BoxDecoration(
              border: Border.all(color: theme.colorScheme.outline),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.calendar_today,
                  size: 16,
                  color: theme.colorScheme.onSurface.withOpacity(0.6),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    dateRangeText,
                    style: TextStyle(
                      fontSize: 14,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                ),
                if (_startDate != null && _endDate != null)
                  IconButton(
                    icon: const Icon(Icons.clear, size: 16),
                    onPressed: () {
                      setState(() {
                        _startDate = null;
                        _endDate = null;
                      });
                    },
                    tooltip: 'Clear date range',
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// Build action buttons
  Widget _buildActionButtons(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: LekkyButton(
            text: 'Cancel',
            onPressed: () => Navigator.of(context).pop(),
            type: LekkyButtonType.secondary,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: LekkyButton(
            text: 'Apply',
            onPressed: () {
              Navigator.of(context).pop({
                'filterType': _filterType,
                'sortOrder': _sortOrder,
                'startDate': _startDate,
                'endDate': _endDate,
              });
            },
            type: LekkyButtonType.primary,
          ),
        ),
      ],
    );
  }

  /// Show date range picker
  Future<void> _selectDateRange(BuildContext context) async {
    final initialDateRange = _startDate != null && _endDate != null
        ? DateTimeRange(start: _startDate!, end: _endDate!)
        : null;

    final pickedDateRange = await showDateRangePicker(
      context: context,
      initialDateRange: initialDateRange,
      firstDate: DateTime(2000),
      lastDate: DateTime.now(),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
                  primary: Theme.of(context).colorScheme.primary,
                  onPrimary: Theme.of(context).colorScheme.onPrimary,
                ),
          ),
          child: child!,
        );
      },
    );

    if (pickedDateRange != null) {
      setState(() {
        _startDate = pickedDateRange.start;
        _endDate = pickedDateRange.end;
      });
    }
  }
}
