import 'package:shared_preferences/shared_preferences.dart';
import '../../domain/models/setup_preferences.dart';
import '../../../../core/shared/models/date_format.dart';

/// Repository for managing setup preferences
class PreferencesRepository {
  /// Save preferences to SharedPreferences
  Future<void> savePreferences(SetupPreferences preferences) async {
    await preferences.saveToPreferences();
  }

  /// Load preferences from SharedPreferences
  Future<SetupPreferences> loadPreferences() async {
    return await SetupPreferences.loadFromPreferences();
  }

  /// Check if setup has been completed
  Future<bool> isSetupCompleted() async {
    return await SetupPreferences.isSetupCompleted();
  }

  /// Update date format
  Future<void> updateDateFormat(DateFormat format) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('date_format', format.formatString);
  }

  /// Update show time with date
  Future<void> updateShowTimeWithDate(bool show) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('show_time_with_date', show);
  }

  /// Update alert threshold
  Future<void> updateAlertThreshold(double threshold) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setDouble('alert_threshold', threshold);
  }

  /// Update days in advance
  Future<void> updateDaysInAdvance(int days) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt('days_in_advance', days);
  }

  /// Update initial meter reading
  Future<void> updateInitialMeterReading(double reading) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setDouble('initial_meter_reading', reading);
  }

  /// Update language
  Future<void> updateLanguage(String language) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('language', language);
  }

  /// Update currency
  Future<void> updateCurrency(String currency, String symbol) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('currency', currency);
    await prefs.setString('currency_symbol', symbol);
  }

  /// Mark setup as completed
  Future<void> markSetupCompleted() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('setup_completed', true);
  }
}
