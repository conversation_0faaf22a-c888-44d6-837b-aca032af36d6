import 'package:flutter/material.dart';
import '../models/reminder_frequency.dart';

/// A widget for selecting reminder frequency
class ReminderFrequencySelector extends StatelessWidget {
  /// Currently selected frequency
  final ReminderFrequency selectedFrequency;
  
  /// Callback when frequency changes
  final Function(ReminderFrequency) onFrequencyChanged;
  
  /// Whether the selector is disabled
  final bool isDisabled;
  
  /// Constructor
  const ReminderFrequencySelector({
    super.key,
    required this.selectedFrequency,
    required this.onFrequencyChanged,
    this.isDisabled = false,
  });

  @override
  Widget build(BuildContext context) {
    return Opacity(
      opacity: isDisabled ? 0.5 : 1.0,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Reminder Frequency',
            style: TextStyle(
              fontWeight: FontWeight.w500,
              fontSize: 16,
            ),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: [
              _buildFrequencyOption(
                context,
                ReminderFrequency.daily,
                'Daily',
              ),
              _buildFrequencyOption(
                context,
                ReminderFrequency.weekly,
                'Weekly',
              ),
              _buildFrequencyOption(
                context,
                ReminderFrequency.biWeekly,
                'Bi-Weekly',
              ),
              _buildFrequencyOption(
                context,
                ReminderFrequency.monthly,
                'Monthly',
              ),
            ],
          ),
        ],
      ),
    );
  }
  
  Widget _buildFrequencyOption(
    BuildContext context,
    ReminderFrequency frequency,
    String label,
  ) {
    final isSelected = selectedFrequency == frequency;
    
    return InkWell(
      onTap: isDisabled ? null : () => onFrequencyChanged(frequency),
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 8,
        ),
        decoration: BoxDecoration(
          color: isSelected
              ? Theme.of(context).colorScheme.primary.withOpacity(0.1)
              : Colors.transparent,
          border: Border.all(
            color: isSelected
                ? Theme.of(context).colorScheme.primary
                : Theme.of(context).dividerColor,
            width: isSelected ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Text(
          label,
          style: TextStyle(
            color: isSelected
                ? Theme.of(context).colorScheme.primary
                : Theme.of(context).textTheme.bodyLarge?.color,
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          ),
        ),
      ),
    );
  }
}
