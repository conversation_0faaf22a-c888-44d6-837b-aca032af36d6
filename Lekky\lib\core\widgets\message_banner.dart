import 'dart:async';
import 'package:flutter/material.dart';
import 'package:marquee/marquee.dart';
import '../theme/app_colors.dart';

class MessageBanner extends StatefulWidget {
  final String? message;
  final List<String>? messages;
  final double height;
  final Color? backgroundColor;

  const MessageBanner({
    super.key,
    this.message,
    this.messages,
    this.height = 32,
    this.backgroundColor,
  }) : assert(message != null || messages != null);

  @override
  State<MessageBanner> createState() => _MessageBannerState();
}

class _MessageBannerState extends State<MessageBanner> {
  int _currentMessageIndex = 0;
  Timer? _messageTimer;

  @override
  void initState() {
    super.initState();
    _startMessageTimer();
  }

  @override
  void dispose() {
    _messageTimer?.cancel();
    super.dispose();
  }

  void _startMessageTimer() {
    if (widget.messages != null && widget.messages!.length > 1) {
      // Calculate timing: each message should scroll twice
      // Velocity 50px/s, typical message ~300px = ~6s per scroll
      // 2 scrolls + 1s pause between = ~13s total per message
      _messageTimer = Timer.periodic(const Duration(seconds: 15), (timer) {
        if (mounted) {
          _updateMessage();
        }
      });
    }
  }

  void _updateMessage() {
    setState(() {
      _currentMessageIndex =
          (_currentMessageIndex + 1) % widget.messages!.length;
    });
  }

  String _getCurrentMessage() {
    if (widget.messages != null && widget.messages!.isNotEmpty) {
      return widget.messages![_currentMessageIndex];
    }
    return widget.message!;
  }

  @override
  Widget build(BuildContext context) {
    final message = _getCurrentMessage();
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final bgColor = widget.backgroundColor ??
        (isDarkMode
            ? AppColors.messageBannerDark
            : AppColors.messageBannerLight);
    final textColor =
        isDarkMode ? AppColors.messageTextDark : AppColors.messageTextLight;

    return Material(
      color: bgColor,
      child: SizedBox(
        height: widget.height,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Marquee(
            key: ValueKey(message), // Force rebuild when message changes
            text: message,
            style: TextStyle(
              color: textColor,
              fontSize: 14,
              height: 1.0,
              fontWeight: FontWeight.w400,
            ),
            scrollAxis: Axis.horizontal,
            crossAxisAlignment: CrossAxisAlignment.center,
            blankSpace: 120.0,
            velocity: 50.0,
            pauseAfterRound: const Duration(seconds: 1),
            startPadding: 0.0,
            accelerationDuration: const Duration(milliseconds: 500),
            accelerationCurve: Curves.easeInOut,
            decelerationDuration: const Duration(milliseconds: 500),
            decelerationCurve: Curves.easeInOut,
            // Let it scroll infinitely, timer will change message
          ),
        ),
      ),
    );
  }
}
