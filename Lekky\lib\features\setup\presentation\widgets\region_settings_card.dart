import 'package:flutter/material.dart';
import 'setup_section_header.dart';
import 'radio_option.dart';

/// A widget for region settings in the setup screen
class RegionSettingsCard extends StatelessWidget {
  /// Current language
  final String language;

  /// Current currency
  final String currency;

  /// Currency symbol
  final String currencySymbol;

  /// Callback when language changes
  final Function(String) onLanguageChanged;

  /// Callback when currency changes
  final Function(String, String) onCurrencyChanged;

  /// Constructor
  const RegionSettingsCard({
    Key? key,
    required this.language,
    required this.currency,
    required this.currencySymbol,
    required this.onLanguageChanged,
    required this.onCurrencyChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(8.0),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.0),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SetupSectionHeader(
              title: 'Region Settings',
              description: 'Language / Currency',
              icon: Icons.language,
            ),

            // Language Subsection
            const Text(
              'Language',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            const Text(
              'Select your preferred language for the app interface.',
              style: TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 8),

            _buildLanguageOptions(),

            const SizedBox(height: 24),

            // Currency Subsection
            const Text(
              'Currency',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            const Text(
              'Select the currency for your meter readings.',
              style: TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 8),

            _buildCurrencyOptions(),

            const SizedBox(height: 8),

            Text(
              'Tip: Select the currency that matches your electricity bills.',
              style: TextStyle(
                fontSize: 12,
                fontStyle: FontStyle.italic,
                color: Theme.of(context).textTheme.bodySmall?.color,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLanguageOptions() {
    return Column(
      children: [
        RadioOption<String>(
          value: 'English',
          groupValue: language,
          onChanged: onLanguageChanged,
          title: 'English',
          icon: Icons.language,
        ),
        RadioOption<String>(
          value: 'Spanish',
          groupValue: language,
          onChanged: onLanguageChanged,
          title: 'Spanish',
          icon: Icons.language,
        ),
        RadioOption<String>(
          value: 'French',
          groupValue: language,
          onChanged: onLanguageChanged,
          title: 'French',
          icon: Icons.language,
        ),
        RadioOption<String>(
          value: 'German',
          groupValue: language,
          onChanged: onLanguageChanged,
          title: 'German',
          icon: Icons.language,
        ),
        RadioOption<String>(
          value: 'Italian',
          groupValue: language,
          onChanged: onLanguageChanged,
          title: 'Italian',
          icon: Icons.language,
        ),
        RadioOption<String>(
          value: 'Portuguese',
          groupValue: language,
          onChanged: onLanguageChanged,
          title: 'Portuguese',
          icon: Icons.language,
        ),
        RadioOption<String>(
          value: 'Russian',
          groupValue: language,
          onChanged: onLanguageChanged,
          title: 'Russian',
          icon: Icons.language,
        ),
        RadioOption<String>(
          value: 'Chinese',
          groupValue: language,
          onChanged: onLanguageChanged,
          title: 'Chinese',
          icon: Icons.language,
        ),
        RadioOption<String>(
          value: 'Japanese',
          groupValue: language,
          onChanged: onLanguageChanged,
          title: 'Japanese',
          icon: Icons.language,
        ),
        RadioOption<String>(
          value: 'Hindi',
          groupValue: language,
          onChanged: onLanguageChanged,
          title: 'Hindi',
          icon: Icons.language,
        ),
      ],
    );
  }

  Widget _buildCurrencyOptions() {
    // Define the currencies
    final currencies = [
      {'code': 'USD', 'symbol': '\$', 'name': 'US Dollar'},
      {'code': 'EUR', 'symbol': '€', 'name': 'Euro'},
      {'code': 'GBP', 'symbol': '£', 'name': 'British Pound'},
      {'code': 'JPY', 'symbol': '¥', 'name': 'Japanese Yen'},
      {'code': 'CNY', 'symbol': 'CN¥', 'name': 'Chinese Yuan'},
      {'code': 'INR', 'symbol': '₹', 'name': 'Indian Rupee'},
      {'code': 'BRL', 'symbol': 'R\$', 'name': 'Brazilian Real'},
      {'code': 'RUB', 'symbol': '₽', 'name': 'Russian Ruble'},
      {'code': 'CAD', 'symbol': 'C\$', 'name': 'Canadian Dollar'},
    ];

    // Split currencies into two columns
    final int midPoint = (currencies.length / 2).ceil();
    final firstColumn = currencies.sublist(0, midPoint);
    final secondColumn = currencies.sublist(midPoint);

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // First column
        Expanded(
          child: Column(
            children: firstColumn.map((currencyData) {
              return RadioOption<String>(
                value: currencyData['code']!,
                groupValue: currency,
                onChanged: (value) =>
                    onCurrencyChanged(value, currencyData['symbol']!),
                title: '${currencyData['symbol']}${currencyData['code']}',
              );
            }).toList(),
          ),
        ),

        // Second column
        Expanded(
          child: Column(
            children: secondColumn.map((currencyData) {
              return RadioOption<String>(
                value: currencyData['code']!,
                groupValue: currency,
                onChanged: (value) =>
                    onCurrencyChanged(value, currencyData['symbol']!),
                title: '${currencyData['symbol']}${currencyData['code']}',
              );
            }).toList(),
          ),
        ),
      ],
    );
  }
}
