// File: lib/features/backup/backup_errors.dart
import '../../core/utils/app_error.dart';
import '../../core/utils/error_types.dart';

/// Types of backup errors
enum BackupErrorType {
  /// File I/O error
  fileIOError,
  
  /// Permission error
  permission,
  
  /// Empty data
  emptyData,
  
  /// Invalid format
  invalidFormat,
  
  /// Version mismatch
  versionMismatch,
  
  /// Parse error
  parseError,
  
  /// User cancelled
  userCancelled,
  
  /// Unknown error
  unknown,
}

/// Create a backup error
AppError createBackupError(
  BackupErrorType type,
  String message, {
  dynamic details,
}) {
  ErrorType errorType;
  ErrorSeverity severity;
  
  switch (type) {
    case BackupErrorType.fileIOError:
      errorType = ErrorType.fileIOError;
      severity = ErrorSeverity.medium;
      break;
    case BackupErrorType.permission:
      errorType = ErrorType.permission;
      severity = ErrorSeverity.medium;
      break;
    case BackupErrorType.emptyData:
      errorType = ErrorType.validation;
      severity = ErrorSeverity.medium;
      break;
    case BackupErrorType.invalidFormat:
      errorType = ErrorType.validation;
      severity = ErrorSeverity.medium;
      break;
    case BackupErrorType.versionMismatch:
      errorType = ErrorType.validation;
      severity = ErrorSeverity.medium;
      break;
    case BackupErrorType.parseError:
      errorType = ErrorType.validation;
      severity = ErrorSeverity.medium;
      break;
    case BackupErrorType.userCancelled:
      errorType = ErrorType.userCancelled;
      severity = ErrorSeverity.low;
      break;
    case BackupErrorType.unknown:
      errorType = ErrorType.unknown;
      severity = ErrorSeverity.high;
      break;
  }
  
  return AppError(
    message: message,
    type: errorType,
    severity: severity,
    details: details,
  );
}

/// Get a user-friendly error message for a backup error
String getBackupErrorMessage(BackupErrorType type) {
  switch (type) {
    case BackupErrorType.fileIOError:
      return 'Could not access the file. Please check if the file exists and you have permission to read it.';
    case BackupErrorType.permission:
      return 'Permission denied. Please grant storage permission to the app.';
    case BackupErrorType.emptyData:
      return 'The backup file is empty or contains no valid data.';
    case BackupErrorType.invalidFormat:
      return 'The file format is invalid. Please select a valid Lekky backup file.';
    case BackupErrorType.versionMismatch:
      return 'This backup was made with an incompatible version of Lekky.';
    case BackupErrorType.parseError:
      return 'Could not parse the backup file. The file may be corrupted.';
    case BackupErrorType.userCancelled:
      return 'Operation cancelled by user.';
    case BackupErrorType.unknown:
      return 'An unexpected error occurred. Please try again later.';
  }
}

/// Get a user-friendly suggestion for a backup error
String getBackupErrorSuggestion(BackupErrorType type) {
  switch (type) {
    case BackupErrorType.fileIOError:
      return 'Try selecting a different file or check if the file is accessible.';
    case BackupErrorType.permission:
      return 'Go to your device settings and grant storage permission to the app.';
    case BackupErrorType.emptyData:
      return 'Make sure you are selecting a valid Lekky backup file.';
    case BackupErrorType.invalidFormat:
      return 'The file should be a CSV file exported from Lekky.';
    case BackupErrorType.versionMismatch:
      return 'Try updating your app to the latest version.';
    case BackupErrorType.parseError:
      return 'Try exporting the data again from the source device.';
    case BackupErrorType.userCancelled:
      return 'You can try again when you are ready.';
    case BackupErrorType.unknown:
      return 'If the problem persists, try restarting the app or your device.';
  }
}
