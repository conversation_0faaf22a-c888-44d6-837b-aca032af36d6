// File: lib/features/cost/domain/utils/cost_message_provider.dart
import '../models/cost_period.dart';

/// Provider for cost-related messages
class CostMessageProvider {
  /// Get a message based on the average usage and period
  static String getMessage(double averageUsage, CostPeriod period) {
    // If no usage data, show a generic message
    if (averageUsage <= 0) {
      return _getGenericMessage();
    }

    // Get a message based on the period
    switch (period.name) {
      case 'Day':
        return _getDailyMessage(averageUsage, period.isPast);
      case 'Week':
        return _getWeeklyMessage(averageUsage, period.isPast);
      case 'Month':
        return _getMonthlyMessage(averageUsage, period.isPast);
      case 'Year':
        return _getYearlyMessage(averageUsage, period.isPast);
      case 'Custom':
        return _getCustomMessage(averageUsage);
      default:
        return _getGenericMessage();
    }
  }

  /// Get a message for daily usage
  static String _getDailyMessage(double averageUsage, bool isPast) {
    if (isPast) {
      if (averageUsage < 1.0) {
        return 'Your daily usage is very low. Great job conserving energy!';
      } else if (averageUsage < 3.0) {
        return 'Your daily usage is moderate. Consider ways to reduce further.';
      } else {
        return 'Your daily usage is high. Try to identify energy-intensive activities.';
      }
    } else {
      return 'Projected daily cost based on your average usage pattern.';
    }
  }

  /// Get a message for weekly usage
  static String _getWeeklyMessage(double averageUsage, bool isPast) {
    if (isPast) {
      return 'Weekly view helps identify patterns in your usage.';
    } else {
      return 'Plan your budget with this weekly cost projection.';
    }
  }

  /// Get a message for monthly usage
  static String _getMonthlyMessage(double averageUsage, bool isPast) {
    if (isPast) {
      return 'Monthly view shows your long-term usage pattern.';
    } else {
      return 'Budget for the month with this cost projection.';
    }
  }

  /// Get a message for yearly usage
  static String _getYearlyMessage(double averageUsage, bool isPast) {
    if (isPast) {
      return 'Yearly view helps identify seasonal patterns in your usage.';
    } else {
      return 'Plan your annual budget with this cost projection.';
    }
  }

  /// Get a message for custom period
  static String _getCustomMessage(double averageUsage) {
    return 'Custom date ranges help analyze specific periods of interest.';
  }

  /// Get a generic message
  static String _getGenericMessage() {
    final messages = [
      'Compare costs across different time periods to identify trends.',
      'Use the custom date range to analyze specific periods of interest.',
      'Toggle between past and future modes to analyze historical data or project costs.',
      'The cost breakdown shows detailed information about your usage and costs.',
      'The chart visualizes your usage or cost trend over time.',
      'Switch between cost and usage views to see different perspectives.',
      'Add more meter readings for more accurate cost calculations.',
      'Regular meter readings improve the accuracy of cost projections.',
      'Fix any invalid history entries to ensure accurate cost calculations.',
      'Use the cost screen to budget for future electricity expenses.',
    ];

    // Return a random message
    messages.shuffle();
    return messages.first;
  }
}
