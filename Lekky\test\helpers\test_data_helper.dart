// File: test/helpers/test_data_helper.dart
import 'package:lekky/core/models/meter_entry.dart';

/// Helper class for creating test data with consistent patterns
class TestDataHelper {
  /// Creates a meter reading entry with required parameters
  static MeterEntry createMeterReading({
    required int id,
    required DateTime date,
    required double reading,
    double amountToppedUp = 0.0,
    double? shortAverageAfterTopUp,
    double? totalAverageUpToThisPoint,
  }) {
    return MeterEntry(
      id: id,
      date: date,
      reading: reading,
      amountToppedUp: amountToppedUp,
      typeCode: 0, // 0 = meter reading
      shortAverageAfterTopUp: shortAverageAfterTopUp,
      totalAverageUpToThisPoint: totalAverageUpToThisPoint,
    );
  }

  /// Creates a top-up entry with required parameters
  static MeterEntry createTopUp({
    required int id,
    required DateTime date,
    required double amountToppedUp,
    double reading = 0.0,
  }) {
    return MeterEntry(
      id: id,
      date: date,
      reading: reading,
      amountToppedUp: amountToppedUp,
      typeCode: 1, // 1 = top-up
    );
  }

  /// Creates a list of sample meter readings for testing
  static List<MeterEntry> createSampleMeterReadings() {
    return [
      createMeterReading(
        id: 1,
        date: DateTime(2023, 1, 1),
        reading: 100.0,
        shortAverageAfterTopUp: 0.0,
        totalAverageUpToThisPoint: 2.0,
      ),
      createMeterReading(
        id: 2,
        date: DateTime(2023, 1, 11),
        reading: 80.0,
        shortAverageAfterTopUp: 4.0,
        totalAverageUpToThisPoint: 2.0,
      ),
      createMeterReading(
        id: 3,
        date: DateTime(2023, 1, 21),
        reading: 60.0,
        shortAverageAfterTopUp: 2.0,
        totalAverageUpToThisPoint: 2.0,
      ),
    ];
  }

  /// Creates a list of sample entries with both readings and top-ups
  static List<MeterEntry> createSampleEntriesWithTopUps() {
    return [
      createMeterReading(
        id: 1,
        date: DateTime(2023, 1, 1),
        reading: 100.0,
        shortAverageAfterTopUp: 0.0,
        totalAverageUpToThisPoint: 2.0,
      ),
      createTopUp(
        id: 2,
        date: DateTime(2023, 1, 6),
        amountToppedUp: 20.0,
      ),
      createMeterReading(
        id: 3,
        date: DateTime(2023, 1, 11),
        reading: 80.0,
        shortAverageAfterTopUp: 4.0,
        totalAverageUpToThisPoint: 2.0,
      ),
    ];
  }

  /// Generates large dataset for performance testing (630+ entries)
  static List<MeterEntry> createLargeDataset() {
    final entries = <MeterEntry>[];
    final baseDate = DateTime(2023, 1, 1);

    // Generate 400 meter readings with decreasing values
    for (int i = 0; i < 400; i++) {
      entries.add(createMeterReading(
        id: i + 1,
        date: baseDate.add(Duration(days: i * 2)),
        reading: 1000.0 - (i * 2.5), // Realistic decreasing pattern
        shortAverageAfterTopUp: 2.0 + (i % 3), // Varying averages
        totalAverageUpToThisPoint: 2.5,
      ));
    }

    // Generate 230 top-ups with varying amounts
    for (int i = 0; i < 230; i++) {
      entries.add(createTopUp(
        id: i + 1000,
        date: baseDate.add(Duration(days: i * 3 + 1)),
        amountToppedUp: 15.0 + (i % 10), // Varying top-up amounts
      ));
    }

    // Sort by date for realistic chronological order
    entries.sort((a, b) => a.date.compareTo(b.date));
    return entries;
  }

  /// Creates entries with validation issues for testing validation logic
  static List<MeterEntry> createInvalidEntries() {
    final now = DateTime.now();
    return [
      // Future date (invalid)
      createMeterReading(
        id: 9001,
        date: now.add(const Duration(days: 1)),
        reading: 50.0,
      ),
      // Negative reading (invalid)
      createMeterReading(
        id: 9002,
        date: now.subtract(const Duration(days: 1)),
        reading: -10.0,
      ),
      // Zero top-up (invalid)
      createTopUp(
        id: 9003,
        date: now.subtract(const Duration(days: 2)),
        amountToppedUp: 0.0,
      ),
    ];
  }
}
