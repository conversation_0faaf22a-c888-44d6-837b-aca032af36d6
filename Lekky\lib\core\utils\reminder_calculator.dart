import 'package:timezone/timezone.dart' as tz;

/// Utility class for calculating reminder dates and handling recurring logic
class ReminderCalculator {
  /// Calculate the next reminder dates based on frequency and start time
  static List<DateTime> calculateNextReminders({
    required DateTime startDateTime,
    required String frequency,
    required int count,
  }) {
    final reminders = <DateTime>[];
    final now = DateTime.now();
    
    // Find the first future reminder
    DateTime nextReminder = _findNextReminder(startDateTime, frequency, now);
    
    // Generate the requested number of reminders
    for (int i = 0; i < count; i++) {
      reminders.add(nextReminder);
      nextReminder = _calculateNextOccurrence(nextReminder, frequency);
    }
    
    return reminders;
  }

  /// Find the next reminder date from now
  static DateTime _findNextReminder(
    DateTime startDateTime,
    String frequency,
    DateTime now,
  ) {
    DateTime candidate = startDateTime;
    
    // If start time is in the future, use it
    if (candidate.isAfter(now)) {
      return candidate;
    }
    
    // Calculate next occurrence based on frequency
    while (candidate.isBefore(now) || candidate.isAtSameMomentAs(now)) {
      candidate = _calculateNextOccurrence(candidate, frequency);
    }
    
    return candidate;
  }

  /// Calculate the next occurrence based on frequency
  static DateTime _calculateNextOccurrence(DateTime current, String frequency) {
    switch (frequency.toLowerCase()) {
      case 'daily':
        return current.add(const Duration(days: 1));
      
      case 'weekly':
        return current.add(const Duration(days: 7));
      
      case 'bi-weekly':
        return current.add(const Duration(days: 14));
      
      case 'monthly':
        return _addMonths(current, 1);
      
      default:
        return current.add(const Duration(days: 7)); // Default to weekly
    }
  }

  /// Add months to a date, handling month-end edge cases
  static DateTime _addMonths(DateTime date, int months) {
    int newYear = date.year;
    int newMonth = date.month + months;
    
    // Handle year overflow
    while (newMonth > 12) {
      newMonth -= 12;
      newYear++;
    }
    
    // Handle month-end edge cases (e.g., Jan 31 → Feb 28)
    int newDay = date.day;
    int daysInNewMonth = _getDaysInMonth(newYear, newMonth);
    if (newDay > daysInNewMonth) {
      newDay = daysInNewMonth;
    }
    
    return DateTime(
      newYear,
      newMonth,
      newDay,
      date.hour,
      date.minute,
      date.second,
      date.millisecond,
      date.microsecond,
    );
  }

  /// Get the number of days in a specific month
  static int _getDaysInMonth(int year, int month) {
    return DateTime(year, month + 1, 0).day;
  }

  /// Convert DateTime to timezone-aware TZDateTime for scheduling
  static tz.TZDateTime toTZDateTime(DateTime dateTime) {
    return tz.TZDateTime.from(dateTime, tz.local);
  }

  /// Check if a reminder time has passed (for missed reminder detection)
  static bool hasReminderPassed(DateTime reminderTime) {
    return reminderTime.isBefore(DateTime.now());
  }

  /// Get a user-friendly description of the next reminder
  static String getNextReminderDescription({
    required DateTime startDateTime,
    required String frequency,
  }) {
    final nextReminder = _findNextReminder(startDateTime, frequency, DateTime.now());
    final now = DateTime.now();
    final difference = nextReminder.difference(now);
    
    if (difference.inDays == 0) {
      if (difference.inHours == 0) {
        return 'In ${difference.inMinutes} minutes';
      }
      return 'In ${difference.inHours} hours';
    } else if (difference.inDays == 1) {
      return 'Tomorrow';
    } else if (difference.inDays < 7) {
      return 'In ${difference.inDays} days';
    } else {
      final weeks = (difference.inDays / 7).floor();
      return 'In $weeks week${weeks > 1 ? 's' : ''}';
    }
  }

  /// Validate reminder settings
  static bool isValidReminderConfiguration({
    required DateTime? startDateTime,
    required String frequency,
  }) {
    if (startDateTime == null) return false;
    
    final validFrequencies = ['daily', 'weekly', 'bi-weekly', 'monthly'];
    return validFrequencies.contains(frequency.toLowerCase());
  }
}
