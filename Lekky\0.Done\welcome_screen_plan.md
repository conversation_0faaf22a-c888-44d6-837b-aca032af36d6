# Welcome Screen Plan for NewLekky

## Overview
The welcome screen appears after the splash screen for first-time users. It introduces the app's key features and provides options to get started or restore previous data.

## Visual Design

### Background
- **Color**: Deep blue (`#003087`)
- **Style**: Solid color, no gradients or patterns

### Header Section
- **Logo**:
  - **Position**: Top center, approximately 10% from top
  - **Design**: Same as splash screen - white circular background with stylized electricity meter icon
  - **Size**: Approximately 20% of screen width

- **Welcome Text**:
  - **Text**: "Welcome to Lekky"
  - **Position**: Centered, below the logo
  - **Font**: Sans-serif, bold, white
  - **Size**: Large (approximately 28sp)

- **Subtitle**:
  - **Text**: "Your personal prepaid meter assistant"
  - **Position**: Centered, below the welcome text
  - **Font**: Sans-serif, regular, white
  - **Size**: Medium (approximately 16sp)

### Features Section
- **Layout**: Vertical list of feature items
- **Spacing**: 24dp between items
- **Alignment**: Left-aligned with consistent margins

#### Feature Items (4 total)
Each feature item consists of:

1. **Track Your Usage**:
   - **Icon**: Circular blue background with white target/usage icon
   - **Title**: "Track Your Usage"
   - **Description**: "Monitor your electricity consumption and spending"
   - **Position**: First item in the list

2. **Get Timely Alerts**:
   - **Icon**: Circular blue background with white bell icon
   - **Title**: "Get Timely Alerts"
   - **Description**: "Receive notifications when your balance is running low"
   - **Position**: Second item in the list

3. **View History**:
   - **Icon**: Circular blue background with white clock/history icon
   - **Title**: "View History"
   - **Description**: "See your past meter readings and top-ups"
   - **Position**: Third item in the list

4. **Calculate Costs**:
   - **Icon**: Circular blue background with white dollar/currency icon
   - **Title**: "Calculate Costs"
   - **Description**: "Estimate your electricity costs over different periods"
   - **Position**: Fourth item in the list

#### Feature Item Design
- **Icon Container**:
  - **Size**: 48dp x 48dp
  - **Shape**: Rounded square with 12dp corner radius
  - **Color**: Medium blue (`#1a5db4`)
  - **Icon Color**: White
  - **Icon Size**: 24dp

- **Title**:
  - **Font**: Sans-serif, bold, white
  - **Size**: Medium (approximately 18sp)
  - **Position**: Right of icon, top-aligned

- **Description**:
  - **Font**: Sans-serif, regular, white with slight transparency (85% opacity)
  - **Size**: Small (approximately 14sp)
  - **Position**: Below title, left-aligned with title
  - **Width**: Fill available space

### Action Buttons

#### Restore Data Button (Optional)
- **Text**: "Restore Previous Data" with history/restore icon
- **Position**: Centered, below features section
- **Style**: Outlined button with white border and text
- **Size**: 80% of screen width
- **Padding**: 12dp vertical, 16dp horizontal
- **Border**: 1dp white border, rounded corners (8dp radius)
- **Helper Text**: "Have a backup from another device?" (below button, centered, small white text)

#### Get Started Button
- **Text**: "Get Started" with arrow icon
- **Position**: Bottom of screen, centered
- **Style**: Filled button with light blue background (`#1a75ff`)
- **Size**: 80% of screen width
- **Padding**: 16dp vertical, 16dp horizontal
- **Border Radius**: 8dp
- **Margin**: 24dp from bottom of screen

## Animation and Interaction

### Entry Animation
- Fade in all elements sequentially from top to bottom (300ms total)
- Slight upward movement during fade-in

### Button Interactions
- Subtle scale effect on button press (95% scale)
- Ripple effect on tap

## Functionality

### Navigation Logic
- **Get Started Button**: Navigates to the Setup screen or directly to Home screen
- **Restore Previous Data Button**: Opens data restoration flow
  - Shows file picker or import options
  - Validates backup file
  - Restores data if valid
  - Shows success/error message

### State Management
- Track whether user has seen welcome screen
- Store this information in local storage
- Skip welcome screen on subsequent app launches

## Implementation Details

### Architecture
```
features/welcome/
├── presentation/
│   ├── screens/
│   │   └── welcome_screen.dart
│   ├── widgets/
│   │   ├── feature_item.dart
│   │   └── welcome_buttons.dart
│   └── controllers/
│       └── welcome_controller.dart
└── domain/
    └── usecases/
        ├── mark_welcome_completed.dart
        └── restore_data.dart
```

### Key Components

#### WelcomeScreen Widget
```dart
class WelcomeScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.primaryBlue,
      body: SafeArea(
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 24.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      const SizedBox(height: 40),
                      // Logo
                      Image.asset(
                        'assets/images/lekky_logo.png',
                        width: 80,
                        height: 80,
                      ),
                      const SizedBox(height: 16),
                      // Welcome Text
                      Text(
                        'Welcome to Lekky',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 28,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 8),
                      // Subtitle
                      Text(
                        'Your personal prepaid meter assistant',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 40),
                      // Features
                      FeatureItem(
                        icon: Icons.track_changes,
                        title: 'Track Your Usage',
                        description: 'Monitor your electricity consumption and spending',
                      ),
                      const SizedBox(height: 24),
                      FeatureItem(
                        icon: Icons.notifications,
                        title: 'Get Timely Alerts',
                        description: 'Receive notifications when your balance is running low',
                      ),
                      const SizedBox(height: 24),
                      FeatureItem(
                        icon: Icons.history,
                        title: 'View History',
                        description: 'See your past meter readings and top-ups',
                      ),
                      const SizedBox(height: 24),
                      FeatureItem(
                        icon: Icons.attach_money,
                        title: 'Calculate Costs',
                        description: 'Estimate your electricity costs over different periods',
                      ),
                      const SizedBox(height: 40),
                      // Restore Data Button
                      OutlinedButton.icon(
                        onPressed: () => _showRestoreOptions(context),
                        icon: Icon(Icons.restore, color: Colors.white),
                        label: Text('Restore Previous Data'),
                        style: OutlinedButton.styleFrom(
                          primary: Colors.white,
                          side: BorderSide(color: Colors.white),
                          padding: EdgeInsets.symmetric(
                            vertical: 12,
                            horizontal: 16,
                          ),
                          minimumSize: Size(MediaQuery.of(context).size.width * 0.8, 0),
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Have a backup from another device?',
                        style: TextStyle(
                          color: Colors.white.withOpacity(0.8),
                          fontSize: 12,
                        ),
                      ),
                      const SizedBox(height: 24),
                    ],
                  ),
                ),
              ),
            ),
            // Get Started Button
            Padding(
              padding: const EdgeInsets.all(24.0),
              child: ElevatedButton.icon(
                onPressed: () => _onGetStarted(context),
                icon: Text('Get Started'),
                label: Icon(Icons.arrow_forward),
                style: ElevatedButton.styleFrom(
                  primary: AppColors.accentBlue,
                  padding: EdgeInsets.symmetric(
                    vertical: 16,
                    horizontal: 16,
                  ),
                  minimumSize: Size(MediaQuery.of(context).size.width * 0.8, 0),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showRestoreOptions(BuildContext context) {
    // Show restore options dialog or navigate to restore screen
  }

  void _onGetStarted(BuildContext context) {
    // Mark welcome as completed
    final welcomeController = Provider.of<WelcomeController>(context, listen: false);
    welcomeController.markWelcomeCompleted();
    
    // Navigate to setup or home screen
    Navigator.of(context).pushReplacement(
      MaterialPageRoute(builder: (_) => SetupScreen()),
    );
  }
}
```

#### FeatureItem Widget
```dart
class FeatureItem extends StatelessWidget {
  final IconData icon;
  final String title;
  final String description;

  const FeatureItem({
    Key? key,
    required this.icon,
    required this.title,
    required this.description,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: AppColors.secondaryBlue,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(
            icon,
            color: Colors.white,
            size: 24,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                description,
                style: TextStyle(
                  color: Colors.white.withOpacity(0.85),
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
```

## Testing Plan

### Unit Tests
- Test welcome completion marking
- Test data restoration logic

### Widget Tests
- Test welcome screen rendering
- Test feature items rendering
- Test button interactions

### Integration Tests
- Test navigation to setup screen
- Test data restoration flow

## Accessibility Considerations
- Ensure sufficient contrast between text and background
- Add semantic labels for screen readers
- Support dynamic text sizing
- Ensure touch targets are at least 48x48dp
