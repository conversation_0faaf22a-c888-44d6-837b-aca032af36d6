// File: lib/features/validation/presentation/widgets/issue_card.dart
import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/widgets/app_card.dart';
import '../../../../core/widgets/lekky_button.dart';
import '../../domain/models/validation_issue.dart';

/// A card that displays a validation issue
class IssueCard extends StatelessWidget {
  /// The validation issue to display
  final ValidationIssue issue;

  /// Callback when the fix button is pressed
  final Function(ValidationIssue) onFix;

  /// Callback when the ignore button is pressed
  final Function(ValidationIssue)? onIgnore;

  /// Callback when the card is tapped
  final Function(ValidationIssue)? onTap;

  /// Whether the card is selected
  final bool isSelected;

  /// Whether selection mode is active
  final bool selectionMode;

  /// Callback when the selection state changes
  final Function(bool)? onSelectionChanged;

  /// Constructor
  const IssueCard({
    super.key,
    required this.issue,
    required this.onFix,
    this.onIgnore,
    this.onTap,
    this.isSelected = false,
    this.selectionMode = false,
    this.onSelectionChanged,
  });

  @override
  Widget build(BuildContext context) {
    return AppCard(
      onTap: selectionMode
          ? () => onSelectionChanged?.call(!isSelected)
          : () => onTap?.call(issue),
      child: Stack(
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with issue type and severity
              Row(
                children: [
                  _buildIssueTypeIcon(),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      _getIssueTypeText(),
                      style: AppTextStyles.titleMedium.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  _buildSeverityBadge(),
                ],
              ),
              const Divider(),

              // Issue message
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 8.0),
                child: Text(
                  issue.message,
                  style: AppTextStyles.bodyMedium,
                ),
              ),

              // Entry ID if available
              if (issue.entryId != null)
                Padding(
                  padding: const EdgeInsets.only(bottom: 8.0),
                  child: Text(
                    'Entry ID: ${issue.entryId}',
                    style: AppTextStyles.bodySmall.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ),

              // Detection date
              Padding(
                padding: const EdgeInsets.only(bottom: 8.0),
                child: Text(
                  'Detected: ${_formatDate(issue.detectedAt)}',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
              ),

              // Action buttons
              if (!selectionMode)
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    LekkyButton(
                      text: 'Edit Entry',
                      type: LekkyButtonType.special,
                      size: LekkyButtonSize.compact,
                      onPressed: () => onFix(issue),
                    ),
                  ],
                ),
            ],
          ),

          // Selection checkbox
          if (selectionMode)
            Positioned(
              top: 0,
              right: 0,
              child: Checkbox(
                value: isSelected,
                onChanged: (value) => onSelectionChanged?.call(value ?? false),
              ),
            ),
        ],
      ),
    );
  }

  /// Build the issue type icon
  Widget _buildIssueTypeIcon() {
    IconData iconData;
    Color iconColor;

    switch (issue.type) {
      case ValidationIssueType.negativeValue:
        iconData = Icons.remove_circle_outline;
        iconColor = Colors.red;
        break;

      case ValidationIssueType.futureDate:
        iconData = Icons.event_busy;
        iconColor = Colors.orange;
        break;

      case ValidationIssueType.chronologicalOrder:
        iconData = Icons.swap_vert;
        iconColor = Colors.red;
        break;

      case ValidationIssueType.balanceInconsistency:
        iconData = Icons.account_balance_wallet;
        iconColor = Colors.red;
        break;

      case ValidationIssueType.duplicateEntry:
        iconData = Icons.content_copy;
        iconColor = Colors.orange;
        break;

      case ValidationIssueType.missingEntry:
        iconData = Icons.calendar_today;
        iconColor = Colors.blue;
        break;

      case ValidationIssueType.other:
        iconData = Icons.error_outline;
        iconColor = Colors.red;
        break;
    }

    return Icon(
      iconData,
      color: iconColor,
      size: 24,
    );
  }

  /// Build the severity badge
  Widget _buildSeverityBadge() {
    Color badgeColor;
    String severityText;

    switch (issue.severity) {
      case ValidationIssueSeverity.high:
        badgeColor = Colors.red;
        severityText = 'High';
        break;

      case ValidationIssueSeverity.medium:
        badgeColor = Colors.orange;
        severityText = 'Medium';
        break;

      case ValidationIssueSeverity.low:
        badgeColor = Colors.blue;
        severityText = 'Low';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: badgeColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        severityText,
        style: AppTextStyles.bodySmall.copyWith(
          color: Colors.white,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  /// Get the issue type text
  String _getIssueTypeText() {
    switch (issue.type) {
      case ValidationIssueType.negativeValue:
        return 'Negative Value';

      case ValidationIssueType.futureDate:
        return 'Future Date';

      case ValidationIssueType.chronologicalOrder:
        return 'Chronological Order';

      case ValidationIssueType.balanceInconsistency:
        return 'Balance Inconsistency';

      case ValidationIssueType.duplicateEntry:
        return 'Duplicate Entry';

      case ValidationIssueType.missingEntry:
        return 'Missing Entry';

      case ValidationIssueType.other:
        return 'Other Issue';
    }
  }

  /// Format a date for display
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }
}
